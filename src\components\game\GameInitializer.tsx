'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { GeminiClient } from '@/lib/gemini';
import { SystemState } from '@/types/game';

interface GameInitializerProps {
  onGameInitialized: (systemState: SystemState) => void;
  apiKey: string;
}

export function GameInitializer({ onGameInitialized, apiKey }: GameInitializerProps) {
  const [isInitializing, setIsInitializing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const initializeNewGame = async () => {
    if (!apiKey) {
      setError('Please set your Gemini API key in settings first.');
      return;
    }

    setIsInitializing(true);
    setError(null);

    try {
      const geminiClient = new GeminiClient(apiKey, 'gemini-2.5-flash');
      const initialState = await geminiClient.initializeGame();
      onGameInitialized(initialState);
    } catch (err) {
      console.error('Failed to initialize game:', err);
      setError('Failed to initialize game. Please check your API key and try again.');
    } finally {
      setIsInitializing(false);
    }
  };

  const startWithDefaultState = () => {
    // Use the default state from useSystemState
    const defaultState: SystemState = {
      profile: {
        name: "Vikram Singh",
        title: "The Hidden King",
        level: 1,
        stamina: 100,
        maxStamina: 100,
        lust: 65,
        maxLust: 100,
        dominance: 10,
        manipulation: 8,
        deception: 12,
        corruption: 15,
        alphaPoints: 0,
        deviantXP: 700,
        systemPoints: 170,
        wealth: 2120000000,
        skills: ['Deception (Lvl 2)', 'Mental Resonance Scan (Lvl 1)'],
        traits: ['Incestuous Aura (Passive)'],
        harem: [],
        inventory: [
          {
            id: 'sleeping_pills',
            name: 'Sleeping Pills (Mild)',
            type: 'Consumable',
            description: 'Mild sedative pills for inducing deep sleep',
            quantity: 1
          }
        ]
      },
      activeQuests: [
        {
          id: 'queen_conquest_2',
          title: 'The Queen\'s Conquest (Stage 2)',
          type: 'Dark',
          target: 'Sonia Singh',
          objective: 'The seed of corruption has been planted. Now, it must be watered with a physical transgression. Administer a second dose of [Sleeping Pills]. While she sleeps, violate her physically.',
          status: 'Active',
          requirements: {
            item: 'Sleeping Pills (Mild)'
          },
          rewards: {
            systemPoints: 1000,
            deviantXP: 1000,
            newSkill: 'Dominator\'s Touch',
            haremUpdate: {
              targetId: 'sonia_singh',
              submissionChange: 25,
              corruptionStageUp: true
            }
          }
        }
      ],
      eventLog: [
        {
          id: 'log_1',
          timestamp: new Date('2012-08-28T22:15:00Z'),
          content: 'Quest Accepted: The Queen\'s Conquest (Stage 1)',
          type: 'Quest'
        },
        {
          id: 'log_2',
          timestamp: new Date('2012-08-28T23:30:00Z'),
          content: 'Bonus Objective Complete: Psychoactive Catalyst. Trait Unlocked: [Incestuous Aura]',
          type: 'Reward'
        },
        {
          id: 'system_boot',
          timestamp: new Date(),
          content: '[SYSTEM BOOT SEQUENCE COMPLETE] [CHIMERA OS v1.0 ONLINE] [BIOMETRIC LINK ESTABLISHED... WELCOME, VIKRAM SINGH]',
          type: 'System'
        }
      ],
      gameSettings: {
        wealthConversionRate: 100000,
        notificationVerbosity: 'Full',
        theme: 'Chimera Purple',
        selectedModel: 'gemini-2.5-flash',
        apiKey: apiKey,
        autoSave: true,
        explicitContent: true,
      },
      isLoading: false,
    };

    onGameInitialized(defaultState);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-purple-900 via-black to-cyan-900 flex items-center justify-center p-4">
      <Card variant="system" className="w-full max-w-2xl">
        <CardHeader className="text-center">
          <CardTitle className="text-3xl font-bold text-primary mb-2">
            VIKRAM SINGH SYSTEM OS
          </CardTitle>
          <p className="text-secondary text-lg">
            CHIMERA v1.0 - INITIALIZATION PROTOCOL
          </p>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="text-center space-y-4">
            <p className="text-muted-foreground">
              The world outside your bedroom door is quiet. The events of the day—the lawyer, 
              the revelation of the 200 crores, the acceptance of the [Queen's Conquest] quest—swirl 
              in your mind. The air in the room feels electric, charged with potential.
            </p>
            <p className="text-primary font-semibold">
              Your phone screen now displays the sleek, dark interface of the System OS.
            </p>
          </div>

          <div className="space-y-4">
            <Button 
              onClick={initializeNewGame}
              disabled={isInitializing || !apiKey}
              variant="corruption"
              className="w-full h-12 text-lg"
            >
              {isInitializing ? 'INITIALIZING SYSTEM...' : 'INITIALIZE WITH AI SYSTEM'}
            </Button>

            <Button 
              onClick={startWithDefaultState}
              variant="quest-dark"
              className="w-full h-12 text-lg"
            >
              START GAME (DEFAULT STATE)
            </Button>
          </div>

          {error && (
            <div className="bg-red-900/20 border border-red-500 rounded p-4 text-red-400">
              {error}
            </div>
          )}

          {!apiKey && (
            <div className="bg-yellow-900/20 border border-yellow-500 rounded p-4 text-yellow-400">
              <p className="font-semibold">API Key Required</p>
              <p className="text-sm">
                To use AI-generated content, please set your Gemini API key in the settings.
                You can still start with the default state.
              </p>
            </div>
          )}

          <div className="text-center text-xs text-muted-foreground">
            <p>This is a mature, adult-oriented interactive fiction experience.</p>
            <p>By proceeding, you confirm you are 18+ and consent to explicit content.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
