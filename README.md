# <PERSON><PERSON><PERSON> System OS

A dark, sophisticated personal operating system for <PERSON><PERSON><PERSON> - a manipulative anti-hero's tool for dominance, corruption, and harem management. Built with Next.js and featuring a sleek, modern interface with purple/cyan accents.

## 🎯 System Features

- **VikramHUD**: Real-time monitoring of stamina, lust, corruption levels, and system currencies
- **Quest Management**: Track Main, Side, Bonus, and Dark quests with color-coded priorities
- **Harem Management**: Detailed profiles of targets with corruption stages, submission levels, and loyalty tracking
- **System Shop**: Purchase consumables, espionage tools, enhancements, and gifts using System Points
- **Event Log**: Reverse-chronological feed of all system activities and notifications
- **Wealth Conversion**: Convert real-world INR to System Points for purchases
- **Dark Theme**: Venomous purple and clinical cyan color scheme for a sinister, high-tech feel

## 🚀 System Initialization

### Prerequisites

- Node.js 18+
- Optional: Google Gemini API key for AI-powered features

### Installation

1. Install dependencies:
```bash
npm install
```

2. Run the development server:
```bash
npm run dev
```

3. Open [http://localhost:3000](http://localhost:3000) to access the System OS

## 🎯 System Usage

1. **Monitor VikramHUD**: Track your corruption level, system currencies, and harem status
2. **Manage Quests**: View active objectives and complete them for rewards
3. **Control Harem**: Interact with targets, track their corruption stages and submission levels
4. **Shop for Tools**: Purchase items using System Points to enhance your capabilities
5. **Review Event Log**: Monitor all system activities and notifications
6. **Configure Settings**: Adjust wealth conversion rates and system preferences

## 🎭 System Components

### VikramHUD
- **Profile Display**: Name, title, level, and corruption meter
- **Resource Tracking**: Stamina, lust, and their maximum values
- **Attribute Monitoring**: Dominance, manipulation, and deception stats
- **Currency Management**: System Points, Alpha Points, Deviant XP, and INR wealth
- **Harem Overview**: Total member count and status

### Quest System
- **Main Quests**: Purple-bordered primary objectives for major progression
- **Side Quests**: Cyan-bordered secondary objectives for additional rewards
- **Dark Quests**: Red-bordered morally questionable objectives with high rewards
- **Bonus Quests**: Special limited-time opportunities

### Harem Management
- **Corruption Stages**: 5-stage progression system for each target
- **Submission Tracking**: 0-100% submission level monitoring
- **Loyalty System**: Relationship stability and devotion measurement
- **Status Classification**: Asset, Pawn, Queen, or Broken status levels

### System Shop
- **Consumables**: Stamina boosters, lust enhancers, confidence pills
- **Espionage Tools**: Surveillance kits, phone trackers, social analyzers
- **Enhancements**: Permanent ability upgrades and skill improvements
- **Gifts**: Luxury items for harem member relationship building

## 🛠️ Technical Architecture

- **Next.js 15**: Modern React framework with App Router
- **TypeScript**: Full type safety throughout the application
- **Tailwind CSS**: Dark theme with purple/cyan accent colors
- **Radix UI**: Accessible component primitives for tabs and dialogs
- **Inter Font**: Modern, clean typography for the System OS interface
- **Local Storage**: Automatic state persistence and recovery
- **Responsive Design**: Optimized for desktop and mobile interfaces

## 💰 Economy System

### Currencies
- **System Points (SP)**: Primary currency for shop purchases
- **Alpha Points (AP)**: Earned from dominant actions and quest completion
- **Deviant XP (DXP)**: Gained from taboo activities and dark quests
- **INR Wealth**: Real-world money convertible to System Points

### Conversion Rates
- **Premium**: ₹50,000 = 1 SP
- **Standard**: ₹1,00,000 = 1 SP
- **Economy**: ₹2,00,000 = 1 SP

## ⚠️ Content Advisory

This system contains mature themes including:
- Psychological manipulation and power dynamics
- Adult relationships and explicit content
- Morally complex anti-hero protagonist
- Dark themes of corruption and dominance
- Harem management and relationship control

Intended for mature audiences. User discretion advised.
