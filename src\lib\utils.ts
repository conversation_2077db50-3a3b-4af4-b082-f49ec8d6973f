import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { VikramProfile } from "@/types/game"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatNumber(num: number): string {
  if (num >= 1000000000) {
    return (num / 1000000000).toFixed(1) + 'B';
  }
  if (num >= 1000000) {
    return (num / 1000000).toFixed(1) + 'M';
  }
  if (num >= 1000) {
    return (num / 1000).toFixed(1) + 'K';
  }
  return num.toString();
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-IN', {
    style: 'currency',
    currency: 'INR',
    maximumFractionDigits: 0,
  }).format(amount);
}

export function getVikramTitle(profile: VikramProfile): string {
  const { level, corruption, dominance } = profile;

  if (corruption >= 90) {
    return "Dark Emperor";
  } else if (corruption >= 75) {
    return "Corruption Master";
  } else if (corruption >= 60) {
    return "Shadow Manipulator";
  } else if (corruption >= 45) {
    return "Puppet Master";
  } else if (corruption >= 30) {
    return "Taboo Breaker";
  } else if (corruption >= 15) {
    return "System Initiate";
  } else {
    return "Awakened One";
  }
}

export function getCorruptionStageDescription(stage: number): string {
  switch (stage) {
    case 1: return "Initial Contact - Building trust and rapport";
    case 2: return "Growing Interest - Emotional manipulation begins";
    case 3: return "Emotional Dependency - Target becomes reliant";
    case 4: return "Physical Submission - Intimate control established";
    case 5: return "Complete Devotion - Total psychological dominance";
    default: return "Unknown Stage";
  }
}

export function getStatusColor(status: string): string {
  switch (status) {
    case 'Asset': return 'text-green-400';
    case 'Pawn': return 'text-yellow-400';
    case 'Queen': return 'text-purple-400';
    case 'Broken': return 'text-red-400';
    default: return 'text-muted-foreground';
  }
}

export function calculateLevelFromCorruption(corruption: number): number {
  return Math.floor(corruption / 10) + 1;
}

export function generateSystemId(): string {
  return `sys_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

export function clampValue(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}
