'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { HaremMember } from '@/types/game';
import { 
  Heart, 
  Shield, 
  Flame, 
  Eye, 
  Star,
  MessageCircle,
  Camera,
  Gift
} from 'lucide-react';

interface CharacterProfileProps {
  character: HaremMember;
  onInteract: (interactionType: string) => void;
  onUpdateCharacter: (updates: Partial<HaremMember>) => void;
}

export function CharacterProfile({ character, onInteract, onUpdateCharacter }: CharacterProfileProps) {
  const [selectedInteraction, setSelectedInteraction] = useState<string>('');

  const getCorruptionStageText = (stage: number) => {
    switch (stage) {
      case 1: return 'Innocent';
      case 2: return 'Curious';
      case 3: return 'Tempted';
      case 4: return 'Corrupted';
      case 5: return 'Devoted';
      default: return 'Unknown';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Asset': return 'text-green-400';
      case 'Pawn': return 'text-yellow-400';
      case 'Queen': return 'text-purple-400';
      case 'Broken': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getCharacterDescription = (character: HaremMember) => {
    switch (character.id) {
      case 'sonia_singh':
        return {
          age: 42,
          description: 'Your mother. A beautiful, mature woman who has been struggling financially since your father\'s death. She works as an accountant, unaware of the family\'s true wealth. Recently widowed and sexually frustrated.',
          physicalTraits: ['38D-32-40', 'Mature Beauty', 'Elegant Posture', 'Tired Eyes'],
          mentalState: 'Stressed about finances, lonely, protective of family',
          weaknesses: ['Financial desperation', 'Maternal guilt', 'Sexual frustration', 'Trust in family'],
          currentMood: 'Exhausted but determined'
        };
      case 'natasha_singh':
        return {
          age: 24,
          description: 'Your elder sister. Busty and confident, working as an HR executive. She dreams of marrying into a better family, unaware that she already belongs to one of the richest.',
          physicalTraits: ['36D-28-38', 'Busty Figure', 'Professional Attire', 'Confident Stride'],
          mentalState: 'Ambitious, slightly arrogant, marriage-focused',
          weaknesses: ['Social status obsession', 'Career ambition', 'Family loyalty', 'Romantic naivety'],
          currentMood: 'Frustrated with current prospects'
        };
      case 'tanya_singh':
        return {
          age: 20,
          description: 'Your younger sister. Petite and energetic, also working in HR. She\'s the most innocent of the family, with a fiery temper that masks her vulnerability.',
          physicalTraits: ['32B-24-34', 'Petite Frame', 'Youthful Energy', 'Expressive Eyes'],
          mentalState: 'Optimistic, naive, easily influenced',
          weaknesses: ['Youthful inexperience', 'Desire for approval', 'Family dependence', 'Emotional volatility'],
          currentMood: 'Hopeful but concerned about family finances'
        };
      default:
        return {
          age: 0,
          description: 'Unknown character',
          physicalTraits: [],
          mentalState: 'Unknown',
          weaknesses: [],
          currentMood: 'Unknown'
        };
    }
  };

  const characterDetails = getCharacterDescription(character);

  const interactionOptions = [
    { id: 'observe', label: 'Observe Quietly', icon: Eye },
    { id: 'casual_talk', label: 'Casual Conversation', icon: MessageCircle },
    { id: 'gift', label: 'Give Gift', icon: Gift },
    { id: 'surveillance', label: 'Install Surveillance', icon: Camera },
    { id: 'manipulation', label: 'Psychological Manipulation', icon: Star },
  ];

  return (
    <Card variant="harem" className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-primary">{character.name}</CardTitle>
            <p className="text-secondary text-sm">{character.designation} • Age {characterDetails.age}</p>
          </div>
          <div className={`text-sm font-semibold ${getStatusColor(character.status)}`}>
            {character.status}
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Character Stats */}
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-3">
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm flex items-center gap-1">
                  <Heart className="w-3 h-3 text-red-400" />
                  Submission
                </span>
                <span className="text-xs text-blue-400">{character.submissionLevel}%</span>
              </div>
              <Progress value={character.submissionLevel} className="h-2" />
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm flex items-center gap-1">
                  <Shield className="w-3 h-3 text-green-400" />
                  Loyalty
                </span>
                <span className="text-xs text-green-400">{character.loyalty}%</span>
              </div>
              <Progress value={character.loyalty} className="h-2" />
            </div>
          </div>
          
          <div className="space-y-3">
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm flex items-center gap-1">
                  <Flame className="w-3 h-3 text-orange-400" />
                  Lust
                </span>
                <span className="text-xs text-orange-400">{character.lustMeter}%</span>
              </div>
              <Progress value={character.lustMeter} className="h-2" />
            </div>
            
            <div>
              <div className="flex items-center justify-between mb-1">
                <span className="text-sm">Corruption Stage</span>
                <span className="text-xs text-purple-400">
                  {character.corruptionStage}/5 ({getCorruptionStageText(character.corruptionStage)})
                </span>
              </div>
              <Progress value={(character.corruptionStage / 5) * 100} className="h-2" />
            </div>
          </div>
        </div>

        {/* Character Description */}
        <div className="space-y-3">
          <h4 className="text-sm font-semibold text-secondary">Profile Analysis</h4>
          <p className="text-xs text-muted-foreground leading-relaxed">
            {characterDetails.description}
          </p>
          
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div>
              <h5 className="font-semibold text-secondary mb-1">Physical Traits</h5>
              <ul className="space-y-1 text-muted-foreground">
                {characterDetails.physicalTraits.map((trait, index) => (
                  <li key={index}>• {trait}</li>
                ))}
              </ul>
            </div>
            
            <div>
              <h5 className="font-semibold text-secondary mb-1">Weaknesses</h5>
              <ul className="space-y-1 text-muted-foreground">
                {characterDetails.weaknesses.map((weakness, index) => (
                  <li key={index}>• {weakness}</li>
                ))}
              </ul>
            </div>
          </div>
          
          <div>
            <h5 className="font-semibold text-secondary mb-1">Current State</h5>
            <p className="text-xs text-muted-foreground">
              <strong>Mental:</strong> {characterDetails.mentalState}
            </p>
            <p className="text-xs text-muted-foreground">
              <strong>Mood:</strong> {characterDetails.currentMood}
            </p>
          </div>
        </div>

        {/* Special Skills */}
        <div>
          <h4 className="text-sm font-semibold text-secondary mb-2">Special Skills</h4>
          <div className="flex flex-wrap gap-1">
            {character.specialSkills.map((skill, index) => (
              <span 
                key={index}
                className="px-2 py-1 bg-purple-900/30 text-purple-300 text-xs rounded"
              >
                {skill}
              </span>
            ))}
          </div>
        </div>

        {/* Interaction Options */}
        <div>
          <h4 className="text-sm font-semibold text-secondary mb-2">Available Interactions</h4>
          <div className="grid grid-cols-1 gap-2">
            {interactionOptions.map((option) => {
              const IconComponent = option.icon;
              return (
                <Button
                  key={option.id}
                  variant="quest-dark"
                  size="sm"
                  className="justify-start h-8 text-xs"
                  onClick={() => onInteract(option.id)}
                >
                  <IconComponent className="w-3 h-3 mr-2" />
                  {option.label}
                </Button>
              );
            })}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
