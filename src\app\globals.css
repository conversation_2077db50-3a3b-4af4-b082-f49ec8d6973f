@import "tailwindcss";

:root {
  /* <PERSON><PERSON><PERSON> OS Color Palette */
  --background: #0a0a0a;
  --foreground: #e5e7eb;
  --card: #111827;
  --card-foreground: #e5e7eb;
  --popover: #111827;
  --popover-foreground: #e5e7eb;
  --primary: #8b5cf6; /* Venomous purple - System power */
  --primary-foreground: #ffffff;
  --secondary: #22d3ee; /* Clinical cyan - Info/stats */
  --secondary-foreground: #0a0a0a;
  --muted: #6b7280;
  --muted-foreground: #9ca3af;
  --accent: #1f2937;
  --accent-foreground: #f9fafb;
  --destructive: #ef4444; /* Blood red - Negative/debuffs */
  --destructive-foreground: #ffffff;
  --border: #374151;
  --input: #374151;
  --ring: #8b5cf6;

  /* System-specific colors */
  --corruption: #8b5cf6;
  --corruption-glow: rgba(139, 92, 246, 0.3);
  --dominance: #dc2626;
  --lust: #ec4899;
  --stamina: #f59e0b;
  --success: #10b981;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  overflow-x: hidden;
}

/* System OS Scrollbar Styles */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thumb-purple-600 {
  scrollbar-color: var(--primary) transparent;
}

.scrollbar-track-slate-700 {
  scrollbar-color: var(--background) transparent;
}

/* Webkit scrollbar styles */
.scrollbar-thin::-webkit-scrollbar {
  width: 8px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: var(--background);
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: #7c3aed;
}

/* System OS Styling */
.system-glow {
  box-shadow: 0 0 20px var(--corruption-glow);
}

.corruption-meter {
  background: linear-gradient(135deg, var(--corruption) 0%, #a855f7 100%);
  box-shadow: 0 0 15px var(--corruption-glow);
}

.quest-main {
  border-color: var(--primary);
  box-shadow: 0 0 10px rgba(139, 92, 246, 0.2);
}

.quest-side {
  border-color: var(--secondary);
  box-shadow: 0 0 10px rgba(34, 211, 238, 0.2);
}

.quest-dark {
  border-color: var(--destructive);
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.2);
}

.harem-card {
  background: linear-gradient(135deg, var(--card) 0%, #1f2937 100%);
  border: 1px solid var(--border);
  transition: all 0.3s ease;
}

.harem-card:hover {
  border-color: var(--primary);
  box-shadow: 0 0 15px var(--corruption-glow);
  transform: translateY(-2px);
}

.stat-bar {
  background: linear-gradient(90deg, transparent 0%, var(--primary) 100%);
  border-radius: 4px;
  overflow: hidden;
}

/* System Event Log Prose Styles */
.prose {
  max-width: none;
}

.prose p {
  margin-bottom: 1rem;
  line-height: 1.7;
}

.prose h1, .prose h2, .prose h3, .prose h4 {
  color: var(--primary);
  margin-top: 1.5rem;
  margin-bottom: 0.5rem;
}

.prose strong {
  color: var(--secondary);
  font-weight: 600;
}

.prose em {
  color: var(--lust);
  font-style: italic;
}

/* System notification types */
.system-notification {
  color: var(--secondary);
}

.quest-notification {
  color: var(--primary);
}

.reward-notification {
  background: linear-gradient(90deg, var(--primary), #a855f7);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Line clamp utility */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
