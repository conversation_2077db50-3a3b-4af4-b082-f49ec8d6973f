'use client';

import React, { useState, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { GeminiClient } from '@/lib/gemini';
import { SystemState, VikramProfile } from '@/types/game';

interface NarrativeEngineProps {
  systemState: SystemState;
  onSystemUpdate: (updates: Partial<SystemState>) => void;
  onProfileUpdate: (updates: Partial<VikramProfile>) => void;
  onAddLogEntry: (entry: { content: string; type: 'System' | 'Quest' | 'Reward' | 'Observation' }) => void;
}

export function NarrativeEngine({ 
  systemState, 
  onSystemUpdate, 
  onProfileUpdate, 
  onAddLogEntry 
}: NarrativeEngineProps) {
  const [currentNarrative, setCurrentNarrative] = useState<string>('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [playerInput, setPlayerInput] = useState('');
  const [availableActions, setAvailableActions] = useState<string[]>([
    'Open the Quest Log to review current objectives',
    'Check the System Shop for available items',
    'Examine your current inventory',
    'Review character profiles in the Harem Management',
    'Use Fallback Mode (No AI required)',
    'Take a custom action...'
  ]);

  // Fallback narrative responses when AI is unavailable
  const getFallbackResponse = (action: string): string => {
    const responses = {
      'Open the Quest Log to review current objectives': `[SYSTEM ANALYSIS] You access the quest database. Current active quests are displayed with their objectives and requirements. The dark interface glows with purple light as you review your targets and their corruption progress.`,

      'Check the System Shop for available items': `[SYSTEM SHOP ACCESS] The shop interface materializes before you. Various items are available for purchase using System Points: surveillance equipment, enhancement drugs, manipulation tools, and gifts for your targets.`,

      'Examine your current inventory': `[INVENTORY SCAN] Your current possessions are catalogued by the System. You have ${systemState.profile.inventory.length} items in your inventory, including any tools and consumables acquired through your manipulative endeavors.`,

      'Review character profiles in the Harem Management': `[CHARACTER DATABASE] The System displays detailed profiles of your targets. Each shows their current corruption stage, submission level, loyalty, and psychological weaknesses. The data updates in real-time as you influence them.`,

      'Use Fallback Mode (No AI required)': `[FALLBACK MODE ACTIVATED] The System switches to local processing mode. While AI-generated content is unavailable due to rate limits, you can still navigate the interface, review stats, manage quests, and use the save/load system. The core game functionality remains intact.`,

      'default': `[SYSTEM PROCESSING] Your action is noted by the System. The dark interface responds to your command, updating relevant statistics and maintaining your progress. The omniscient System continues to monitor your manipulative activities.`
    };

    return responses[action] || responses['default'];
  };

  const processPlayerAction = useCallback(async (action: string, customInput?: string) => {
    // Check if we should use fallback mode
    if (!systemState.gameSettings.apiKey || action === 'Use Fallback Mode (No AI required)') {
      const fallbackResponse = getFallbackResponse(action);
      setCurrentNarrative(fallbackResponse);

      onAddLogEntry({
        content: `Action taken: ${customInput || action}`,
        type: 'Observation'
      });

      return;
    }

    setIsProcessing(true);
    
    try {
      const geminiClient = new GeminiClient(
        systemState.gameSettings.apiKey, 
        systemState.gameSettings.selectedModel
      );

      const actionText = customInput || action;
      const response = await geminiClient.generateSystemResponse(
        systemState,
        actionText,
        'Player action in the Vikram Singh System OS'
      );

      // Update narrative
      setCurrentNarrative(response.narrative);

      // Apply system updates
      if (response.systemUpdates) {
        const profileUpdates: Partial<VikramProfile> = {};
        
        if (response.systemUpdates.corruption !== undefined) {
          profileUpdates.corruption = Math.min(100, Math.max(0, response.systemUpdates.corruption));
        }
        if (response.systemUpdates.dominance !== undefined) {
          profileUpdates.dominance = Math.max(0, response.systemUpdates.dominance);
        }
        if (response.systemUpdates.manipulation !== undefined) {
          profileUpdates.manipulation = Math.max(0, response.systemUpdates.manipulation);
        }
        if (response.systemUpdates.systemPoints !== undefined) {
          profileUpdates.systemPoints = Math.max(0, response.systemUpdates.systemPoints);
        }
        if (response.systemUpdates.deviantXP !== undefined) {
          profileUpdates.deviantXP = Math.max(0, response.systemUpdates.deviantXP);
        }
        if (response.systemUpdates.alphaPoints !== undefined) {
          profileUpdates.alphaPoints = Math.max(0, response.systemUpdates.alphaPoints);
        }

        if (Object.keys(profileUpdates).length > 0) {
          onProfileUpdate(profileUpdates);
        }
      }

      // Apply character updates
      if (response.characterUpdates) {
        const updatedHarem = systemState.profile.harem.map(member => {
          const updates = response.characterUpdates[member.id];
          if (updates) {
            return {
              ...member,
              submissionLevel: updates.submissionLevel !== undefined 
                ? Math.min(100, Math.max(0, updates.submissionLevel))
                : member.submissionLevel,
              loyalty: updates.loyalty !== undefined
                ? Math.min(100, Math.max(0, updates.loyalty))
                : member.loyalty,
              lustMeter: updates.lustMeter !== undefined
                ? Math.min(100, Math.max(0, updates.lustMeter))
                : member.lustMeter,
              corruptionStage: updates.corruptionStage !== undefined
                ? Math.min(5, Math.max(1, updates.corruptionStage)) as 1 | 2 | 3 | 4 | 5
                : member.corruptionStage
            };
          }
          return member;
        });

        onProfileUpdate({ harem: updatedHarem });
      }

      // Add new quests
      if (response.newQuests && response.newQuests.length > 0) {
        const updatedQuests = [...systemState.activeQuests, ...response.newQuests];
        onSystemUpdate({ activeQuests: updatedQuests });
        
        response.newQuests.forEach(quest => {
          onAddLogEntry({
            content: `New quest available: ${quest.title}`,
            type: 'Quest'
          });
        });
      }

      // Log the action
      onAddLogEntry({
        content: `Action taken: ${actionText}`,
        type: 'Observation'
      });

    } catch (error) {
      console.error('Error processing player action:', error);

      // Check if it's a rate limit error
      if (error.message.includes('429') || error.message.includes('quota')) {
        setCurrentNarrative(`[SYSTEM OVERLOAD] The AI System is currently experiencing high demand. Rate limits exceeded. Please wait a few minutes before trying again, or consider upgrading your API plan for higher limits.

You can still play using the default narrative responses while the AI system recovers.`);
      } else {
        setCurrentNarrative('System Error: Failed to process action. Please check your API key configuration and try again.');
      }
    } finally {
      setIsProcessing(false);
      setPlayerInput('');
    }
  }, [systemState, onSystemUpdate, onProfileUpdate, onAddLogEntry]);

  const handleCustomAction = () => {
    if (playerInput.trim()) {
      processPlayerAction('Take a custom action', playerInput.trim());
    }
  };

  return (
    <Card variant="system" className="h-full">
      <CardHeader>
        <CardTitle className="text-primary">NARRATIVE ENGINE</CardTitle>
        <p className="text-secondary text-sm">Real-time AI-powered story progression</p>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Current Narrative Display */}
        <div className="bg-black/40 border border-purple-500/30 rounded p-4 min-h-[200px]">
          <h3 className="text-primary font-semibold mb-2">[SYSTEM NARRATIVE]</h3>
          <div className="text-muted-foreground whitespace-pre-wrap">
            {currentNarrative || 'The System awaits your command. What is your first action?'}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="space-y-2">
          <h4 className="text-secondary font-semibold">Available Actions:</h4>
          {availableActions.slice(0, -1).map((action, index) => (
            <Button
              key={index}
              onClick={() => processPlayerAction(action)}
              disabled={isProcessing}
              variant="quest-dark"
              className="w-full text-left justify-start h-auto py-2 px-3"
            >
              {action}
            </Button>
          ))}
        </div>

        {/* Custom Action Input */}
        <div className="space-y-2">
          <h4 className="text-secondary font-semibold">Custom Action:</h4>
          <div className="flex gap-2">
            <input
              type="text"
              value={playerInput}
              onChange={(e) => setPlayerInput(e.target.value)}
              placeholder="Describe your action..."
              className="flex-1 bg-black/40 border border-purple-500/30 rounded px-3 py-2 text-white placeholder-gray-400"
              disabled={isProcessing}
              onKeyPress={(e) => e.key === 'Enter' && handleCustomAction()}
            />
            <Button
              onClick={handleCustomAction}
              disabled={isProcessing || !playerInput.trim()}
              variant="corruption"
            >
              Execute
            </Button>
          </div>
        </div>

        {isProcessing && (
          <div className="text-center text-primary">
            <p>Processing action...</p>
            <div className="animate-pulse">The System is analyzing your choice...</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
