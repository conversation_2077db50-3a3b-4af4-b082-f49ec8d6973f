'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { GameSettings } from '@/types/game';
import { 
  Key, 
  Eye, 
  EyeOff,
  CheckCircle,
  AlertTriangle,
  ExternalLink,
  X
} from 'lucide-react';

interface ApiKeyManagerProps {
  settings: GameSettings;
  onUpdateSettings: (updates: Partial<GameSettings>) => void;
  onClose: () => void;
}

export function ApiKeyManager({ settings, onUpdateSettings, onClose }: ApiKeyManagerProps) {
  const [apiKey, setApiKey] = useState(settings.apiKey);
  const [showKey, setShowKey] = useState(false);
  const [isValidating, setIsValidating] = useState(false);
  const [validationMessage, setValidationMessage] = useState('');
  const [validationStatus, setValidationStatus] = useState<'success' | 'error' | ''>('');

  const validateApiKey = async (key: string) => {
    if (!key || key.length < 10) {
      setValidationMessage('API key appears to be invalid (too short)');
      setValidationStatus('error');
      return false;
    }

    if (!key.startsWith('AIza')) {
      setValidationMessage('API key should start with "AIza"');
      setValidationStatus('error');
      return false;
    }

    setValidationMessage('API key format looks correct');
    setValidationStatus('success');
    return true;
  };

  const handleSave = async () => {
    setIsValidating(true);
    
    const isValid = await validateApiKey(apiKey);
    
    if (isValid) {
      onUpdateSettings({ apiKey });
      setValidationMessage('API key saved successfully!');
      setValidationStatus('success');
      
      setTimeout(() => {
        onClose();
      }, 1500);
    }
    
    setIsValidating(false);
  };

  const handleKeyChange = (value: string) => {
    setApiKey(value);
    setValidationMessage('');
    setValidationStatus('');
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card variant="system" className="w-full max-w-2xl">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-primary">
              <Key className="w-5 h-5" />
              API Key Management
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Current Status */}
          <div className="bg-black/40 border border-purple-500/30 rounded p-4">
            <h3 className="text-secondary font-semibold mb-2">Current Status</h3>
            <div className="flex items-center gap-2">
              {settings.apiKey ? (
                <>
                  <CheckCircle className="w-4 h-4 text-green-400" />
                  <span className="text-green-400">API Key Configured</span>
                </>
              ) : (
                <>
                  <AlertTriangle className="w-4 h-4 text-yellow-400" />
                  <span className="text-yellow-400">No API Key Set</span>
                </>
              )}
            </div>
          </div>

          {/* API Key Input */}
          <div className="space-y-3">
            <label className="text-sm font-semibold text-secondary">
              Gemini API Key
            </label>
            <div className="relative">
              <input
                type={showKey ? 'text' : 'password'}
                value={apiKey}
                onChange={(e) => handleKeyChange(e.target.value)}
                placeholder="Enter your Gemini API key (AIza...)"
                className="w-full bg-black/40 border border-purple-500/30 rounded px-3 py-2 pr-10 text-white placeholder-gray-400 focus:border-purple-500 focus:outline-none"
              />
              <Button
                variant="ghost"
                size="sm"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                onClick={() => setShowKey(!showKey)}
              >
                {showKey ? <EyeOff className="w-3 h-3" /> : <Eye className="w-3 h-3" />}
              </Button>
            </div>
          </div>

          {/* Validation Message */}
          {validationMessage && (
            <div className={`p-3 rounded border ${
              validationStatus === 'success' 
                ? 'bg-green-900/20 border-green-500 text-green-400' 
                : 'bg-red-900/20 border-red-500 text-red-400'
            }`}>
              <div className="flex items-center gap-2">
                {validationStatus === 'success' ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  <AlertTriangle className="w-4 h-4" />
                )}
                {validationMessage}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-black/40 border border-cyan-500/30 rounded p-4">
            <h3 className="text-primary font-semibold mb-2">How to Get Your API Key</h3>
            <ol className="text-sm text-muted-foreground space-y-2">
              <li>1. Visit Google AI Studio</li>
              <li>2. Sign in with your Google account</li>
              <li>3. Click "Get API Key" or "Create API Key"</li>
              <li>4. Copy the generated key (starts with "AIza")</li>
              <li>5. Paste it in the field above</li>
            </ol>
            <Button
              variant="outline"
              size="sm"
              className="mt-3"
              onClick={() => window.open('https://aistudio.google.com/apikey', '_blank')}
            >
              <ExternalLink className="w-3 h-3 mr-2" />
              Open Google AI Studio
            </Button>
          </div>

          {/* Rate Limit Information */}
          <div className="bg-black/40 border border-yellow-500/30 rounded p-4">
            <h3 className="text-yellow-400 font-semibold mb-2">Rate Limit Information</h3>
            <div className="text-sm text-muted-foreground space-y-1">
              <p>• Free tier: Limited requests per minute/day</p>
              <p>• If you hit limits, wait or upgrade to paid plan</p>
              <p>• The game has fallback mode when AI is unavailable</p>
              <p>• Consider using gemini-1.5-flash for higher limits</p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex gap-3">
            <Button
              variant="system"
              onClick={handleSave}
              disabled={isValidating || !apiKey}
              className="flex-1"
            >
              {isValidating ? 'Validating...' : 'Save API Key'}
            </Button>
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>

          {/* Security Note */}
          <div className="text-xs text-muted-foreground text-center">
            <p>Your API key is stored locally in your browser and never sent to our servers.</p>
            <p>Keep your API key secure and don't share it with others.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
