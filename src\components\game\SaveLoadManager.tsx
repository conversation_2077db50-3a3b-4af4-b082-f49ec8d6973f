'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { SystemState } from '@/types/game';
import { 
  Save, 
  Download, 
  Upload,
  Trash2,
  Clock,
  User,
  Star,
  AlertTriangle,
  CheckCircle,
  X
} from 'lucide-react';

interface SaveLoadManagerProps {
  systemState: SystemState;
  onSave: () => boolean;
  onLoad: () => boolean;
  onClose: () => void;
}

interface SaveSlot {
  id: string;
  name: string;
  timestamp: string;
  profile: {
    name: string;
    title: string;
    level: number;
    corruption: number;
    systemPoints: number;
  };
  questCount: number;
  version: string;
}

export function SaveLoadManager({ systemState, onSave, onLoad, onClose }: SaveLoadManagerProps) {
  const [saveSlots, setSaveSlots] = useState<SaveSlot[]>([]);
  const [selectedSlot, setSelectedSlot] = useState<string | null>(null);
  const [saveMessage, setSaveMessage] = useState<string>('');
  const [messageType, setMessageType] = useState<'success' | 'error' | ''>('');

  useEffect(() => {
    loadSaveSlots();
  }, []);

  const loadSaveSlots = () => {
    const slots: SaveSlot[] = [];
    
    // Check for main save
    const mainSave = localStorage.getItem('vikram-system-save');
    if (mainSave) {
      try {
        const saveData = JSON.parse(mainSave);
        slots.push({
          id: 'main',
          name: 'Main Save',
          timestamp: saveData.timestamp,
          profile: {
            name: saveData.profile.name,
            title: saveData.profile.title,
            level: saveData.profile.level,
            corruption: saveData.profile.corruption,
            systemPoints: saveData.profile.systemPoints
          },
          questCount: saveData.activeQuests.length,
          version: saveData.version || '1.0'
        });
      } catch (error) {
        console.error('Error loading main save:', error);
      }
    }

    // Check for additional save slots (1-5)
    for (let i = 1; i <= 5; i++) {
      const slotSave = localStorage.getItem(`vikram-system-save-${i}`);
      if (slotSave) {
        try {
          const saveData = JSON.parse(slotSave);
          slots.push({
            id: `slot-${i}`,
            name: `Save Slot ${i}`,
            timestamp: saveData.timestamp,
            profile: {
              name: saveData.profile.name,
              title: saveData.profile.title,
              level: saveData.profile.level,
              corruption: saveData.profile.corruption,
              systemPoints: saveData.profile.systemPoints
            },
            questCount: saveData.activeQuests.length,
            version: saveData.version || '1.0'
          });
        } catch (error) {
          console.error(`Error loading save slot ${i}:`, error);
        }
      }
    }

    setSaveSlots(slots);
  };

  const handleSave = (slotId: string) => {
    try {
      const saveData = {
        profile: systemState.profile,
        activeQuests: systemState.activeQuests,
        eventLog: systemState.eventLog,
        gameSettings: systemState.gameSettings,
        timestamp: new Date().toISOString(),
        version: '1.0'
      };

      const storageKey = slotId === 'main' ? 'vikram-system-save' : `vikram-system-save-${slotId.split('-')[1]}`;
      localStorage.setItem(storageKey, JSON.stringify(saveData));

      setSaveMessage(`Game saved to ${slotId === 'main' ? 'Main Save' : `Slot ${slotId.split('-')[1]}`}`);
      setMessageType('success');
      loadSaveSlots();

      setTimeout(() => {
        setSaveMessage('');
        setMessageType('');
      }, 3000);

    } catch (error) {
      console.error('Error saving game:', error);
      setSaveMessage('Failed to save game');
      setMessageType('error');
    }
  };

  const handleLoad = (slotId: string) => {
    try {
      const storageKey = slotId === 'main' ? 'vikram-system-save' : `vikram-system-save-${slotId.split('-')[1]}`;
      const savedData = localStorage.getItem(storageKey);
      
      if (!savedData) {
        setSaveMessage('No save data found');
        setMessageType('error');
        return;
      }

      // For now, we'll just trigger the main load function
      // In a more sophisticated implementation, we'd load the specific slot
      if (onLoad()) {
        setSaveMessage('Game loaded successfully');
        setMessageType('success');
        setTimeout(() => {
          onClose();
        }, 1500);
      } else {
        setSaveMessage('Failed to load game');
        setMessageType('error');
      }

    } catch (error) {
      console.error('Error loading game:', error);
      setSaveMessage('Failed to load game');
      setMessageType('error');
    }
  };

  const handleDelete = (slotId: string) => {
    if (confirm('Are you sure you want to delete this save?')) {
      const storageKey = slotId === 'main' ? 'vikram-system-save' : `vikram-system-save-${slotId.split('-')[1]}`;
      localStorage.removeItem(storageKey);
      loadSaveSlots();
      setSaveMessage('Save deleted');
      setMessageType('success');
    }
  };

  const exportSave = () => {
    try {
      const saveData = {
        profile: systemState.profile,
        activeQuests: systemState.activeQuests,
        eventLog: systemState.eventLog,
        gameSettings: { ...systemState.gameSettings, apiKey: '' }, // Don't export API key
        timestamp: new Date().toISOString(),
        version: '1.0'
      };

      const dataStr = JSON.stringify(saveData, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });
      const url = URL.createObjectURL(dataBlob);
      
      const link = document.createElement('a');
      link.href = url;
      link.download = `vikram-save-${new Date().toISOString().split('T')[0]}.json`;
      link.click();
      
      URL.revokeObjectURL(url);
      setSaveMessage('Save exported successfully');
      setMessageType('success');
    } catch (error) {
      console.error('Error exporting save:', error);
      setSaveMessage('Failed to export save');
      setMessageType('error');
    }
  };

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card variant="system" className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-primary">
              <Save className="w-5 h-5" />
              Save & Load Manager
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="w-4 h-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Current Game Status */}
          <div className="bg-black/40 border border-purple-500/30 rounded p-4">
            <h3 className="text-secondary font-semibold mb-2">Current Game Status</h3>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="text-muted-foreground">Character:</span>
                <p className="text-primary">{systemState.profile.name}</p>
                <p className="text-xs text-secondary">{systemState.profile.title}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Level:</span>
                <p className="text-primary">{systemState.profile.level}</p>
              </div>
              <div>
                <span className="text-muted-foreground">Corruption:</span>
                <p className="text-purple-400">{systemState.profile.corruption}/100</p>
              </div>
              <div>
                <span className="text-muted-foreground">System Points:</span>
                <p className="text-secondary">{systemState.profile.systemPoints} SP</p>
              </div>
            </div>
          </div>

          {/* Message Display */}
          {saveMessage && (
            <div className={`p-3 rounded border ${
              messageType === 'success' 
                ? 'bg-green-900/20 border-green-500 text-green-400' 
                : 'bg-red-900/20 border-red-500 text-red-400'
            }`}>
              <div className="flex items-center gap-2">
                {messageType === 'success' ? (
                  <CheckCircle className="w-4 h-4" />
                ) : (
                  <AlertTriangle className="w-4 h-4" />
                )}
                {saveMessage}
              </div>
            </div>
          )}

          {/* Quick Actions */}
          <div className="flex gap-3">
            <Button
              variant="system"
              onClick={() => handleSave('main')}
              className="flex-1"
            >
              <Save className="w-4 h-4 mr-2" />
              Quick Save
            </Button>
            <Button
              variant="corruption"
              onClick={() => handleLoad('main')}
              className="flex-1"
              disabled={!saveSlots.find(s => s.id === 'main')}
            >
              <Download className="w-4 h-4 mr-2" />
              Quick Load
            </Button>
            <Button
              variant="outline"
              onClick={exportSave}
              className="flex-1"
            >
              <Upload className="w-4 h-4 mr-2" />
              Export Save
            </Button>
          </div>

          {/* Save Slots */}
          <div className="space-y-3">
            <h3 className="text-secondary font-semibold">Save Slots</h3>
            {saveSlots.length === 0 ? (
              <Card className="p-6 text-center">
                <p className="text-muted-foreground">No saved games found</p>
              </Card>
            ) : (
              <div className="space-y-2">
                {saveSlots.map((slot) => (
                  <Card key={slot.id} className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h4 className="font-semibold text-primary">{slot.name}</h4>
                          <span className="text-xs text-muted-foreground flex items-center gap-1">
                            <Clock className="w-3 h-3" />
                            {new Date(slot.timestamp).toLocaleString()}
                          </span>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-xs">
                          <div>
                            <span className="text-muted-foreground">Character:</span>
                            <p className="text-primary">{slot.profile.name}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Level:</span>
                            <p className="text-primary">{slot.profile.level}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Corruption:</span>
                            <p className="text-purple-400">{slot.profile.corruption}/100</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Quests:</span>
                            <p className="text-secondary">{slot.questCount} active</p>
                          </div>
                        </div>
                      </div>
                      <div className="flex gap-2 ml-4">
                        <Button
                          variant="system"
                          size="sm"
                          onClick={() => handleLoad(slot.id)}
                        >
                          Load
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleSave(slot.id)}
                        >
                          Overwrite
                        </Button>
                        <Button
                          variant="destructive"
                          size="sm"
                          onClick={() => handleDelete(slot.id)}
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
