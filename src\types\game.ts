// The core profile for our protagonist
export interface V<PERSON>ramProfile {
  name: "<PERSON><PERSON><PERSON>";
  title: string; // e.g., 'Taboo Breaker', 'Puppet Master'
  level: number;

  // Core Resources
  stamina: number;
  maxStamina: number;
  lust: number;
  maxLust: number;

  // Core Attributes
  dominance: number;
  manipulation: number;
  deception: number;

  // System Currencies & Points
  corruption: number; // Scale of 0-100, unlocks darker paths
  alphaPoints: number; // Earned from dominant acts
  deviantXP: number; // Earned from taboo acts
  systemPoints: number; // The main currency (CP)
  wealth: number; // Real-world money (INR)

  // Unlocked Abilities
  skills: string[]; // e.g., 'Vocal Resonance Lvl 1', 'Predator's Stealth'
  traits: string[]; // e.g., 'Incestuous Aura (Passive)'

  // The Harem
  harem: HaremMember[];

  // Inventory
  inventory: SystemItem[];
}

// Structure for each girl in the harem
export interface HaremMember {
  id: string;
  name: string;
  designation: string; // 'Mother', 'Sister', 'Classmate', 'Teacher'
  corruptionStage: 1 | 2 | 3 | 4 | 5;
  submissionLevel: number; // 0-100
  loyalty: number; // 0-100
  lustMeter: number; // 0-100, directed at Vikram
  specialSkills: string[]; // 'Financial Access', 'Gossip Network'
  status: 'Asset' | 'Pawn' | 'Queen' | 'Broken';
  profileImage: string; // Path to an image asset
}

// Structure for items in the shop/inventory
export interface SystemItem {
  id: string;
  name: string;
  type: 'Consumable' | 'Espionage' | 'Enhancement' | 'Gift';
  description: string;
  quantity: number;
}

// Quests are the new "Choices"
export interface Quest {
  id: string;
  title: string;
  type: 'Main' | 'Side' | 'Bonus' | 'Dark';
  target: string; // Name of the target character
  objective: string;
  status: 'Active' | 'Completed' | 'Failed';

  requirements?: {
    skill?: string;
    item?: string;
    corruption?: number;
    dominance?: number;
  };

  rewards: {
    systemPoints?: number;
    deviantXP?: number;
    alphaPoints?: number;
    newSkill?: string;
    newTitle?: string;
    haremUpdate?: {
      targetId: string;
      submissionChange?: number;
      loyaltyChange?: number;
      corruptionStageUp?: boolean;
    };
  };
}

// The main state of the System OS
export interface SystemState {
  profile: VikramProfile;
  activeQuests: Quest[];
  eventLog: LogEntry[];
  gameSettings: GameSettings;
  isLoading: boolean;
}

// An entry in the event log
export interface LogEntry {
  id: string;
  timestamp: Date;
  content: string;
  type: 'System' | 'Quest' | 'Reward' | 'Observation';
}

// System settings
export interface GameSettings {
  wealthConversionRate: number; // e.g., 100000 INR = 100 SP
  notificationVerbosity: 'Full' | 'Minimal';
  theme: 'Chimera Purple' | 'Venom Green'; // Future-proofing
  selectedModel: string;
  apiKey: string;
  autoSave: boolean;
  explicitContent: boolean;
}

export interface GeminiConfig {
  model: string;
  apiKey: string;
  safetySettings: any[];
  generationConfig: {
    temperature: number;
    topP: number;
    topK: number;
    maxOutputTokens: number;
  };
}

export const AVAILABLE_MODELS = [
  'gemini-2.0-flash-exp',
  'gemini-2.0-flash-thinking-exp',
  'gemini-1.5-pro',
  'gemini-1.5-flash',
] as const;

export type ModelType = typeof AVAILABLE_MODELS[number];

// System locations for tracking Vikram's movements
export const SYSTEM_LOCATIONS = [
  'Malviya Nagar Flat',
  'College Campus',
  'Family Home',
  'Shopping District',
  'Corporate Office',
  'Elite Club',
  'Private Residence',
] as const;

export type SystemLocation = typeof SYSTEM_LOCATIONS[number];

// Predefined harem member designations
export const HAREM_DESIGNATIONS = [
  'Mother',
  'Sister',
  'Aunt',
  'Cousin',
  'Classmate',
  'Teacher',
  'Professor',
  'Colleague',
  'Neighbor',
  'Friend',
  'Stranger',
] as const;

export type HaremDesignation = typeof HAREM_DESIGNATIONS[number];

// System moods for the top bar
export const SYSTEM_MOODS = [
  'Predatory Calm',
  'Calculating',
  'Dominant',
  'Lustful',
  'Manipulative',
  'Satisfied',
  'Hunting',
  'Corrupting',
] as const;

export type SystemMood = typeof SYSTEM_MOODS[number];
