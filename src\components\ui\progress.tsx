import * as React from "react"
import { cn } from "@/lib/utils"

interface ProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value?: number
  max?: number
  variant?: 'default' | 'stamina' | 'lust' | 'corruption' | 'dominance' | 'submission' | 'loyalty'
  showGlow?: boolean
}

const Progress = React.forwardRef<HTMLDivElement, ProgressProps>(
  ({ className, value = 0, max = 100, variant = 'default', showGlow = false, ...props }, ref) => {
    const percentage = Math.min(Math.max((value / max) * 100, 0), 100)

    const getVariantClasses = () => {
      switch (variant) {
        case 'stamina':
          return {
            bg: 'bg-amber-900/30',
            fill: 'bg-gradient-to-r from-amber-500 to-orange-500',
            glow: showGlow ? 'shadow-lg shadow-amber-500/30' : ''
          }
        case 'lust':
          return {
            bg: 'bg-pink-900/30',
            fill: 'bg-gradient-to-r from-pink-500 to-purple-500',
            glow: showGlow ? 'shadow-lg shadow-pink-500/30' : ''
          }
        case 'corruption':
          return {
            bg: 'bg-purple-900/30',
            fill: 'bg-gradient-to-r from-purple-600 to-violet-600',
            glow: showGlow ? 'shadow-lg shadow-purple-500/30' : ''
          }
        case 'dominance':
          return {
            bg: 'bg-red-900/30',
            fill: 'bg-gradient-to-r from-red-500 to-red-600',
            glow: showGlow ? 'shadow-lg shadow-red-500/30' : ''
          }
        case 'submission':
          return {
            bg: 'bg-blue-900/30',
            fill: 'bg-gradient-to-r from-blue-400 to-blue-500',
            glow: showGlow ? 'shadow-lg shadow-blue-500/30' : ''
          }
        case 'loyalty':
          return {
            bg: 'bg-green-900/30',
            fill: 'bg-gradient-to-r from-green-400 to-green-500',
            glow: showGlow ? 'shadow-lg shadow-green-500/30' : ''
          }
        default:
          return {
            bg: 'bg-secondary',
            fill: 'bg-primary',
            glow: showGlow ? 'shadow-lg shadow-primary/30' : ''
          }
      }
    }

    const variantClasses = getVariantClasses()

    return (
      <div
        ref={ref}
        className={cn(
          "relative h-4 w-full overflow-hidden rounded-full",
          variantClasses.bg,
          variantClasses.glow,
          className
        )}
        {...props}
      >
        <div
          className={cn("h-full w-full flex-1 transition-all duration-300", variantClasses.fill)}
          style={{ transform: `translateX(-${100 - percentage}%)` }}
        />
        {showGlow && percentage > 0 && (
          <div
            className={cn("absolute inset-0 h-full transition-all duration-300", variantClasses.fill, "opacity-50 blur-sm")}
            style={{ transform: `translateX(-${100 - percentage}%)` }}
          />
        )}
      </div>
    )
  }
)
Progress.displayName = "Progress"

export { Progress }
