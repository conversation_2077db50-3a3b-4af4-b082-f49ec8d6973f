'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Quest, SystemState } from '@/types/game';
import { GeminiClient } from '@/lib/gemini';
import { 
  Target, 
  Crown, 
  Skull,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Eye,
  Zap,
  Heart,
  Shield
} from 'lucide-react';

interface QuestInteractionProps {
  quest: Quest;
  systemState: SystemState;
  onQuestComplete: (questId: string, results: any) => void;
  onQuestUpdate: (questId: string, updates: Partial<Quest>) => void;
  onSystemUpdate: (updates: any) => void;
  onClose: () => void;
}

export function QuestInteraction({ 
  quest, 
  systemState, 
  onQuestComplete, 
  onQuestUpdate, 
  onSystemUpdate, 
  onClose 
}: QuestInteractionProps) {
  const [isProcessing, setIsProcessing] = useState(false);
  const [questNarrative, setQuestNarrative] = useState<string>('');
  const [availableActions, setAvailableActions] = useState<string[]>([]);
  const [questProgress, setQuestProgress] = useState(0);
  const [riskLevel, setRiskLevel] = useState<'Low' | 'Medium' | 'High'>('Medium');

  const getQuestIcon = (type: Quest['type']) => {
    switch (type) {
      case 'Main': return Crown;
      case 'Side': return Target;
      case 'Dark': return Skull;
      default: return Target;
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'Low': return 'text-green-400';
      case 'Medium': return 'text-yellow-400';
      case 'High': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const generateQuestActions = (quest: Quest) => {
    switch (quest.id) {
      case 'queen_conquest_2':
        return [
          'Prepare the sleeping pills carefully',
          'Wait for the perfect moment when she\'s alone',
          'Administer the pills discreetly in her evening tea',
          'Monitor her condition and wait for deep sleep',
          'Proceed with the physical transgression',
          'Document the encounter for System analysis'
        ];
      case 'serpent_nest':
        return [
          'Purchase a high-quality micro spy camera',
          'Study Natasha\'s daily routine and room layout',
          'Wait for her to leave for work',
          'Enter her room and find the optimal camera placement',
          'Install the camera in a concealed location',
          'Test the camera feed and ensure it\'s undetectable'
        ];
      case 'watering_hole':
        return [
          'Acquire a waterproof nano-camera',
          'Study the family\'s bathroom usage patterns',
          'Choose the optimal installation time',
          'Install the camera in a strategic location',
          'Ensure the camera is completely hidden',
          'Set up remote monitoring system'
        ];
      default:
        return [
          'Analyze the situation carefully',
          'Plan your approach strategically',
          'Execute with precision and caution',
          'Monitor the results closely'
        ];
    }
  };

  const processQuestAction = async (action: string) => {
    if (!systemState.gameSettings.apiKey) {
      setQuestNarrative('System Error: API key not configured. Please set your Gemini API key in settings.');
      return;
    }

    setIsProcessing(true);
    
    try {
      const geminiClient = new GeminiClient(
        systemState.gameSettings.apiKey, 
        systemState.gameSettings.selectedModel
      );

      const response = await geminiClient.generateSystemResponse(
        systemState,
        `Quest Action: ${action}`,
        `Player is attempting quest "${quest.title}" with action: ${action}`
      );

      setQuestNarrative(response.narrative);

      // Update quest progress based on action
      const newProgress = Math.min(100, questProgress + 20);
      setQuestProgress(newProgress);

      // Apply system updates
      if (response.systemUpdates) {
        onSystemUpdate(response.systemUpdates);
      }

      // Check if quest is completed
      if (newProgress >= 100) {
        onQuestComplete(quest.id, {
          narrative: response.narrative,
          rewards: quest.rewards,
          systemUpdates: response.systemUpdates
        });
      }

    } catch (error) {
      console.error('Error processing quest action:', error);
      setQuestNarrative('System Error: Failed to process quest action. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };

  const analyzeQuest = async () => {
    if (!systemState.gameSettings.apiKey) {
      setQuestNarrative('System Error: API key not configured.');
      return;
    }

    setIsProcessing(true);
    
    try {
      const geminiClient = new GeminiClient(
        systemState.gameSettings.apiKey, 
        systemState.gameSettings.selectedModel
      );

      const analysisPrompt = `Analyze the quest "${quest.title}" for Vikram Singh. 
      
      Quest Details:
      - Target: ${quest.target}
      - Objective: ${quest.objective}
      - Type: ${quest.type}
      
      Current System State:
      - Corruption: ${systemState.profile.corruption}
      - Dominance: ${systemState.profile.dominance}
      - Manipulation: ${systemState.profile.manipulation}
      
      Provide a detailed analysis including:
      1. Risk assessment
      2. Optimal approach strategy
      3. Potential consequences
      4. Required preparations
      5. Success probability`;

      const response = await geminiClient.generateSystemResponse(
        systemState,
        'Analyze Quest',
        analysisPrompt
      );

      setQuestNarrative(response.narrative);

    } catch (error) {
      console.error('Error analyzing quest:', error);
      setQuestNarrative('System Error: Failed to analyze quest.');
    } finally {
      setIsProcessing(false);
    }
  };

  React.useEffect(() => {
    setAvailableActions(generateQuestActions(quest));
    setQuestNarrative(`Quest "${quest.title}" is ready for execution. Choose your approach carefully.`);
  }, [quest]);

  const Icon = getQuestIcon(quest.type);

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card variant="system" className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-primary">
              <Icon className="w-5 h-5" />
              {quest.title}
            </CardTitle>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <XCircle className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex items-center gap-4 text-sm">
            <span className="text-secondary">Target: {quest.target}</span>
            <span className={`flex items-center gap-1 ${getRiskColor(riskLevel)}`}>
              <AlertTriangle className="w-3 h-3" />
              Risk: {riskLevel}
            </span>
            <span className="text-muted-foreground">Progress: {questProgress}%</span>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Quest Progress */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-semibold">Quest Progress</span>
              <span className="text-sm text-primary">{questProgress}%</span>
            </div>
            <Progress value={questProgress} className="h-2" />
          </div>

          {/* Quest Objective */}
          <div className="bg-black/40 border border-purple-500/30 rounded p-4">
            <h3 className="text-secondary font-semibold mb-2">Objective</h3>
            <p className="text-muted-foreground text-sm">{quest.objective}</p>
          </div>

          {/* Current Narrative */}
          <div className="bg-black/40 border border-cyan-500/30 rounded p-4 min-h-[150px]">
            <h3 className="text-primary font-semibold mb-2">[SYSTEM ANALYSIS]</h3>
            <div className="text-muted-foreground whitespace-pre-wrap text-sm">
              {questNarrative || 'Awaiting your command...'}
            </div>
          </div>

          {/* Available Actions */}
          <div className="space-y-3">
            <h3 className="text-secondary font-semibold">Available Actions</h3>
            <div className="grid grid-cols-1 gap-2">
              {availableActions.map((action, index) => (
                <Button
                  key={index}
                  variant="quest-dark"
                  className="justify-start h-auto py-3 px-4 text-left"
                  onClick={() => processQuestAction(action)}
                  disabled={isProcessing}
                >
                  <span className="text-xs text-purple-400 mr-3">{index + 1}.</span>
                  {action}
                </Button>
              ))}
            </div>
          </div>

          {/* Analysis and Control Buttons */}
          <div className="flex gap-3">
            <Button
              variant="system"
              onClick={analyzeQuest}
              disabled={isProcessing}
              className="flex-1"
            >
              <Eye className="w-4 h-4 mr-2" />
              Analyze Quest
            </Button>
            <Button
              variant="corruption"
              onClick={() => processQuestAction('Execute with maximum aggression')}
              disabled={isProcessing}
              className="flex-1"
            >
              <Zap className="w-4 h-4 mr-2" />
              Aggressive Approach
            </Button>
            <Button
              variant="outline"
              onClick={onClose}
              className="flex-1"
            >
              Cancel
            </Button>
          </div>

          {isProcessing && (
            <div className="text-center text-primary">
              <p>Processing quest action...</p>
              <div className="animate-pulse">The System is calculating outcomes...</div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
