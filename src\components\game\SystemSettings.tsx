'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { AVAILABLE_MODELS, GameSettings, VikramProfile } from '@/types/game';
import { 
  Settings, 
  Brain, 
  Zap, 
  Sparkles, 
  Eye, 
  Coins,
  ArrowRightLeft,
  Star,
  Bell,
  Palette,
  Shield,
  Volume2
} from 'lucide-react';

interface SystemSettingsProps {
  settings: GameSettings;
  profile: VikramProfile;
  onSettingsChange: (settings: Partial<GameSettings>) => void;
  onWealthConversion?: (inrAmount: number) => void;
  onSave: () => void;
}

export function SystemSettings({ 
  settings, 
  profile, 
  onSettingsChange, 
  onWealthConversion, 
  onSave 
}: SystemSettingsProps) {
  const [conversionAmount, setConversionAmount] = useState<string>('');

  const getModelIcon = (model: string) => {
    if (model.includes('2.0')) return Sparkles;
    if (model.includes('thinking')) return Brain;
    if (model.includes('flash')) return Zap;
    return Eye;
  };

  const getModelDescription = (model: string) => {
    switch (model) {
      case 'gemini-2.5-flash':
        return 'Latest free tier model - fast and efficient (Free Tier Available)';
      case 'gemini-2.0-flash-exp':
        return 'Latest experimental model with enhanced capabilities';
      case 'gemini-2.0-flash-thinking-exp':
        return 'Advanced reasoning model with thinking capabilities';
      case 'gemini-1.5-pro':
        return 'Balanced model for complex storytelling';
      case 'gemini-1.5-flash':
        return 'Fast and efficient for quick responses';
      default:
        return 'Gemini AI model for story generation';
    }
  };

  const calculateSPFromINR = (inr: number): number => {
    return Math.floor(inr / settings.wealthConversionRate);
  };

  const handleWealthConversion = () => {
    const inrAmount = parseInt(conversionAmount);
    if (inrAmount && inrAmount > 0 && inrAmount <= profile.wealth) {
      onWealthConversion?.(inrAmount);
      setConversionAmount('');
    }
  };

  const maxConvertibleINR = Math.min(profile.wealth, 1000000); // Max 10 lakh at once
  const spFromConversion = conversionAmount ? calculateSPFromINR(parseInt(conversionAmount) || 0) : 0;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Settings className="w-5 h-5 text-primary" />
        <h2 className="text-lg font-semibold text-foreground">System Settings</h2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Wealth Conversion */}
        <Card variant="system">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-primary">
              <ArrowRightLeft className="w-5 h-5" />
              Wealth Conversion
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <Coins className="w-4 h-4 text-yellow-500" />
                <div>
                  <p className="text-muted-foreground">Available Wealth</p>
                  <p className="font-bold text-yellow-500">₹{profile.wealth.toLocaleString()}</p>
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-primary" />
                <div>
                  <p className="text-muted-foreground">System Points</p>
                  <p className="font-bold text-primary">{profile.systemPoints} SP</p>
                </div>
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">
                Convert INR to System Points
              </label>
              <div className="flex gap-2">
                <input
                  type="number"
                  value={conversionAmount}
                  onChange={(e) => setConversionAmount(e.target.value)}
                  placeholder="Enter INR amount"
                  min="1"
                  max={maxConvertibleINR}
                  className="flex-1 px-3 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <Button
                  variant="system"
                  onClick={handleWealthConversion}
                  disabled={!conversionAmount || parseInt(conversionAmount) > profile.wealth}
                >
                  Convert
                </Button>
              </div>
              {conversionAmount && (
                <p className="text-xs text-muted-foreground">
                  Will receive: <span className="text-primary font-bold">{spFromConversion} SP</span>
                  {' '}(Rate: ₹{settings.wealthConversionRate} = 1 SP)
                </p>
              )}
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">
                Conversion Rate
              </label>
              <Select 
                value={settings.wealthConversionRate.toString()} 
                onValueChange={(value) => onSettingsChange({ wealthConversionRate: parseInt(value) })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="50000">₹50,000 = 1 SP (Premium)</SelectItem>
                  <SelectItem value="100000">₹1,00,000 = 1 SP (Standard)</SelectItem>
                  <SelectItem value="200000">₹2,00,000 = 1 SP (Economy)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* System Configuration */}
        <Card variant="system">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-primary">
              <Settings className="w-5 h-5" />
              System Configuration
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            {/* Notification Settings */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">
                <Bell className="w-4 h-4 inline mr-2" />
                Notification Verbosity
              </label>
              <Select 
                value={settings.notificationVerbosity} 
                onValueChange={(value: any) => onSettingsChange({ notificationVerbosity: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Full">Full - All system events</SelectItem>
                  <SelectItem value="Minimal">Minimal - Important events only</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Theme Selection */}
            <div className="space-y-2">
              <label className="text-sm font-medium text-foreground">
                <Palette className="w-4 h-4 inline mr-2" />
                System Theme
              </label>
              <Select 
                value={settings.theme} 
                onValueChange={(value: any) => onSettingsChange({ theme: value })}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Chimera Purple">Chimera Purple (Current)</SelectItem>
                  <SelectItem value="Venom Green">Venom Green (Coming Soon)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* Auto Save */}
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-foreground">
                Auto Save
              </label>
              <button
                onClick={() => onSettingsChange({ autoSave: !settings.autoSave })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.autoSave ? 'bg-primary' : 'bg-muted'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.autoSave ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>

            {/* Explicit Content */}
            <div className="flex items-center justify-between">
              <label className="text-sm font-medium text-foreground">
                Explicit Content
              </label>
              <button
                onClick={() => onSettingsChange({ explicitContent: !settings.explicitContent })}
                className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                  settings.explicitContent ? 'bg-red-500' : 'bg-muted'
                }`}
              >
                <span
                  className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                    settings.explicitContent ? 'translate-x-6' : 'translate-x-1'
                  }`}
                />
              </button>
            </div>
          </CardContent>
        </Card>

        {/* AI Configuration */}
        <Card variant="system" className="lg:col-span-2">
          <CardHeader className="pb-4">
            <CardTitle className="flex items-center gap-2 text-primary">
              <Brain className="w-5 h-5" />
              AI Configuration
            </CardTitle>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* API Key */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  Gemini API Key
                </label>
                <input
                  type="password"
                  value={settings.apiKey}
                  onChange={(e) => onSettingsChange({ apiKey: e.target.value })}
                  placeholder="Enter your Gemini API key"
                  className="w-full px-3 py-2 bg-input border border-border rounded-md text-foreground placeholder-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                />
                <p className="text-xs text-muted-foreground">
                  Get your API key from{' '}
                  <a 
                    href="https://aistudio.google.com/apikey" 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-primary hover:text-primary/80 underline"
                  >
                    Google AI Studio
                  </a>
                </p>
              </div>

              {/* Model Selection */}
              <div className="space-y-2">
                <label className="text-sm font-medium text-foreground">
                  AI Model
                </label>
                <Select 
                  value={settings.selectedModel} 
                  onValueChange={(value) => onSettingsChange({ selectedModel: value })}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select a model" />
                  </SelectTrigger>
                  <SelectContent>
                    {AVAILABLE_MODELS.map((model) => {
                      const Icon = getModelIcon(model);
                      return (
                        <SelectItem key={model} value={model}>
                          <div className="flex items-center gap-2">
                            <Icon className="w-4 h-4" />
                            <div>
                              <div className="font-medium">{model}</div>
                              <div className="text-xs text-muted-foreground">
                                {getModelDescription(model)}
                              </div>
                            </div>
                          </div>
                        </SelectItem>
                      );
                    })}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Save Button */}
            <Button 
              onClick={onSave}
              variant="system"
              className="w-full"
              disabled={!settings.apiKey || !settings.selectedModel}
            >
              <Shield className="w-4 h-4 mr-2" />
              Save System Configuration
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
