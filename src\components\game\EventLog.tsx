'use client';

import React, { useEffect, useRef } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { LogEntry } from '@/types/game';
import { 
  Activity, 
  Clock, 
  Settings, 
  ScrollText, 
  Gift, 
  Eye,
  AlertCircle,
  CheckCircle,
  Info,
  Zap
} from 'lucide-react';

interface EventLogProps {
  events: LogEntry[];
  maxEvents?: number;
  autoScroll?: boolean;
}

export function EventLog({ events, maxEvents = 50, autoScroll = true }: EventLogProps) {
  const contentRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (autoScroll && contentRef.current) {
      contentRef.current.scrollTop = contentRef.current.scrollHeight;
    }
  }, [events, autoScroll]);

  const getEventIcon = (type: LogEntry['type']) => {
    switch (type) {
      case 'System': return Settings;
      case 'Quest': return ScrollText;
      case 'Reward': return Gift;
      case 'Observation': return Eye;
      default: return Info;
    }
  };

  const getEventColor = (type: LogEntry['type']) => {
    switch (type) {
      case 'System': return 'text-secondary border-l-secondary';
      case 'Quest': return 'text-primary border-l-primary';
      case 'Reward': return 'text-yellow-400 border-l-yellow-400';
      case 'Observation': return 'text-green-400 border-l-green-400';
      default: return 'text-muted-foreground border-l-muted';
    }
  };

  const getEventBackground = (type: LogEntry['type']) => {
    switch (type) {
      case 'System': return 'bg-secondary/5';
      case 'Quest': return 'bg-primary/5';
      case 'Reward': return 'bg-yellow-400/5';
      case 'Observation': return 'bg-green-400/5';
      default: return 'bg-muted/5';
    }
  };

  const formatTimestamp = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const minutes = Math.floor(diff / 60000);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    if (days > 0) {
      return `${days}d ago`;
    } else if (hours > 0) {
      return `${hours}h ago`;
    } else if (minutes > 0) {
      return `${minutes}m ago`;
    } else {
      return 'Just now';
    }
  };

  const formatTime = (date: Date) => {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    }).format(date);
  };

  // Sort events by timestamp (newest first) and limit
  const sortedEvents = [...events]
    .sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime())
    .slice(0, maxEvents);

  return (
    <Card variant="system" className="h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-primary">
          <Activity className="w-5 h-5" />
          System Event Log
        </CardTitle>
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <Clock className="w-3 h-3" />
          <span>Real-time monitoring active</span>
          <span>•</span>
          <span>{events.length} total events</span>
        </div>
      </CardHeader>
      
      <CardContent className="h-full p-0">
        <div 
          ref={contentRef}
          className="h-96 overflow-y-auto scrollbar-thin scrollbar-thumb-purple-600 scrollbar-track-slate-700 px-6 pb-6"
        >
          {sortedEvents.length === 0 ? (
            <div className="flex items-center justify-center h-full text-muted-foreground">
              <div className="text-center">
                <Activity className="w-8 h-8 mx-auto mb-2 opacity-50" />
                <p className="text-sm">No events recorded yet</p>
                <p className="text-xs">System monitoring will appear here</p>
              </div>
            </div>
          ) : (
            <div className="space-y-3">
              {sortedEvents.map((event) => {
                const Icon = getEventIcon(event.type);
                const colorClasses = getEventColor(event.type);
                const bgClasses = getEventBackground(event.type);
                
                return (
                  <div 
                    key={event.id} 
                    className={`p-3 rounded-lg border-l-4 ${colorClasses} ${bgClasses} transition-all duration-200 hover:bg-opacity-80`}
                  >
                    <div className="flex items-start gap-3">
                      <Icon className="w-4 h-4 mt-0.5 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center justify-between mb-1">
                          <span className={`text-xs font-medium px-2 py-1 rounded-full bg-current/10 ${
                            event.type === 'System' ? 'text-secondary' :
                            event.type === 'Quest' ? 'text-primary' :
                            event.type === 'Reward' ? 'text-yellow-400' :
                            event.type === 'Observation' ? 'text-green-400' :
                            'text-muted-foreground'
                          }`}>
                            {event.type}
                          </span>
                          <div className="flex items-center gap-2 text-xs text-muted-foreground">
                            <span>{formatTime(event.timestamp)}</span>
                            <span>•</span>
                            <span>{formatTimestamp(event.timestamp)}</span>
                          </div>
                        </div>
                        
                        <div className={`text-sm ${
                          event.type === 'System' ? 'system-notification' :
                          event.type === 'Quest' ? 'quest-notification' :
                          event.type === 'Reward' ? 'reward-notification' :
                          'text-foreground'
                        }`}>
                          {event.content}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}

// Helper function to create log entries
export const createLogEntry = (
  content: string, 
  type: LogEntry['type'] = 'System'
): LogEntry => ({
  id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
  timestamp: new Date(),
  content,
  type
});

// Predefined log entry creators for common events
export const LogEntries = {
  systemStart: () => createLogEntry('System OS initialized successfully', 'System'),
  questAvailable: (questTitle: string) => createLogEntry(`New quest available: ${questTitle}`, 'Quest'),
  questCompleted: (questTitle: string) => createLogEntry(`Quest completed: ${questTitle}`, 'Quest'),
  questFailed: (questTitle: string) => createLogEntry(`Quest failed: ${questTitle}`, 'Quest'),
  rewardEarned: (reward: string) => createLogEntry(`Reward earned: ${reward}`, 'Reward'),
  skillUnlocked: (skill: string) => createLogEntry(`New skill unlocked: ${skill}`, 'Reward'),
  titleEarned: (title: string) => createLogEntry(`New title earned: ${title}`, 'Reward'),
  haremMemberAdded: (name: string) => createLogEntry(`${name} added to harem`, 'Observation'),
  haremMemberProgression: (name: string, stage: number) => createLogEntry(`${name} progressed to corruption stage ${stage}`, 'Observation'),
  corruptionIncrease: (amount: number) => createLogEntry(`Corruption increased by ${amount} points`, 'System'),
  wealthConversion: (inr: number, sp: number) => createLogEntry(`Converted ₹${inr.toLocaleString()} to ${sp} SP`, 'System'),
  locationChange: (location: string) => createLogEntry(`Location changed to ${location}`, 'System'),
  moodChange: (mood: string) => createLogEntry(`Mood shifted to ${mood}`, 'Observation'),
};
