'use client';

import React from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { VikramProfile } from '@/types/game';
import { 
  Crown, 
  Heart, 
  Zap, 
  Brain, 
  Eye, 
  Users,
  Coins, 
  Star,
  TrendingUp,
  Flame,
  Shield
} from 'lucide-react';

interface VikramHUDProps {
  profile: VikramProfile;
}

export function VikramHUD({ profile }: VikramHUDProps) {
  return (
    <Card variant="system" className="h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-2 text-primary text-xl">
          <Crown className="w-6 h-6" />
          {profile.name}
        </CardTitle>
        <p className="text-sm text-secondary font-medium">{profile.title}</p>
        <div className="flex items-center gap-2 text-xs text-muted-foreground">
          <span>Level {profile.level}</span>
          <span>•</span>
          <span>System User</span>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Core Resources */}
        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-foreground">Resources</h3>
          
          {/* Stamina */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Heart className="w-4 h-4 text-amber-500" />
                <span className="text-sm">Stamina</span>
              </div>
              <span className="text-xs text-muted-foreground">
                {profile.stamina} / {profile.maxStamina}
              </span>
            </div>
            <Progress 
              value={profile.stamina} 
              max={profile.maxStamina}
              variant="stamina"
              showGlow={true}
            />
          </div>

          {/* Lust */}
          <div className="space-y-1">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Flame className="w-4 h-4 text-pink-500" />
                <span className="text-sm">Lust</span>
              </div>
              <span className="text-xs text-muted-foreground">
                {profile.lust} / {profile.maxLust}
              </span>
            </div>
            <Progress 
              value={profile.lust} 
              max={profile.maxLust}
              variant="lust"
              showGlow={true}
            />
          </div>
        </div>

        {/* Core Attributes */}
        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-foreground">Attributes</h3>
          <div className="grid grid-cols-3 gap-3">
            <div className="text-center">
              <Shield className="w-4 h-4 mx-auto text-red-400 mb-1" />
              <p className="text-xs text-muted-foreground">Dominance</p>
              <p className="text-sm font-bold text-red-400">{profile.dominance}</p>
            </div>
            <div className="text-center">
              <Brain className="w-4 h-4 mx-auto text-blue-400 mb-1" />
              <p className="text-xs text-muted-foreground">Manipulation</p>
              <p className="text-sm font-bold text-blue-400">{profile.manipulation}</p>
            </div>
            <div className="text-center">
              <Eye className="w-4 h-4 mx-auto text-green-400 mb-1" />
              <p className="text-xs text-muted-foreground">Deception</p>
              <p className="text-sm font-bold text-green-400">{profile.deception}</p>
            </div>
          </div>
        </div>

        {/* Corruption Meter */}
        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-foreground">Corruption Level</h3>
          <div className="relative">
            <div className="w-24 h-24 mx-auto relative">
              <svg className="w-full h-full transform -rotate-90" viewBox="0 0 100 100">
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="none"
                  className="text-muted"
                />
                <circle
                  cx="50"
                  cy="50"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="none"
                  strokeDasharray={`${(profile.corruption / 100) * 251.2} 251.2`}
                  className="text-primary corruption-meter"
                  strokeLinecap="round"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg font-bold text-primary">{profile.corruption}</span>
              </div>
            </div>
            <p className="text-center text-xs text-muted-foreground mt-2">
              Corruption Level
            </p>
          </div>
        </div>

        {/* Currencies */}
        <div className="space-y-3">
          <h3 className="text-sm font-semibold text-foreground">Currencies</h3>
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Star className="w-4 h-4 text-primary" />
                <span className="text-sm">System Points</span>
              </div>
              <span className="text-sm font-bold text-primary">{profile.systemPoints} SP</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4 text-red-400" />
                <span className="text-sm">Alpha Points</span>
              </div>
              <span className="text-sm font-bold text-red-400">{profile.alphaPoints} AP</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Flame className="w-4 h-4 text-purple-400" />
                <span className="text-sm">Deviant XP</span>
              </div>
              <span className="text-sm font-bold text-purple-400">{profile.deviantXP} DXP</span>
            </div>
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Coins className="w-4 h-4 text-yellow-500" />
                <span className="text-sm">Wealth</span>
              </div>
              <span className="text-sm font-bold text-yellow-500">₹{profile.wealth.toLocaleString()}</span>
            </div>
          </div>
        </div>

        {/* Harem Count */}
        <div className="space-y-2">
          <div className="flex items-center justify-between p-3 bg-gradient-to-r from-primary/10 to-purple-600/10 rounded-lg border border-primary/20">
            <div className="flex items-center gap-2">
              <Users className="w-4 h-4 text-primary" />
              <span className="text-sm font-medium">Harem Members</span>
            </div>
            <span className="text-lg font-bold text-primary">{profile.harem.length}</span>
          </div>
        </div>

        {/* Active Skills */}
        {profile.skills.length > 0 && (
          <div className="space-y-2">
            <h3 className="text-sm font-semibold text-foreground">Active Skills</h3>
            <div className="flex flex-wrap gap-1">
              {profile.skills.slice(0, 3).map((skill, index) => (
                <span 
                  key={index}
                  className="px-2 py-1 text-xs bg-primary/20 border border-primary/30 rounded-full text-primary"
                >
                  {skill}
                </span>
              ))}
              {profile.skills.length > 3 && (
                <span className="px-2 py-1 text-xs bg-muted/20 border border-muted/30 rounded-full text-muted-foreground">
                  +{profile.skills.length - 3} more
                </span>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
