'use client';

import { useState, useEffect, useCallback } from 'react';
import { SystemState, VikramProfile, Quest, LogEntry, GameSettings, HaremMember, SystemItem } from '@/types/game';

const initialSettings: GameSettings = {
  wealthConversionRate: 100000, // ₹1,00,000 = 1 SP
  notificationVerbosity: 'Full',
  theme: 'Chimera Purple',
  selectedModel: 'gemini-2.5-flash',
  apiKey: process.env.NEXT_PUBLIC_GEMINI_API_KEY || '',
  autoSave: true,
  explicitContent: true,
};

const createInitialProfile = (): VikramProfile => ({
  name: "<PERSON><PERSON><PERSON>",
  title: "The Hidden King",
  level: 1,

  // Core Resources
  stamina: 100,
  maxStamina: 100,
  lust: 65,
  maxLust: 100,

  // Core Attributes
  dominance: 10,
  manipulation: 8,
  deception: 12,

  // System Currencies & Points
  corruption: 15,
  alphaPoints: 0,
  deviantXP: 700,
  systemPoints: 170,
  wealth: 2120000000, // ₹212 crores inheritance (hidden from family)

  // Unlocked Abilities
  skills: ['Deception (Lvl 2)', 'Mental Resonance Scan (Lvl 1)'],
  traits: ['Incestuous Aura (Passive)'],

  // The Harem (family members as potential targets)
  harem: [
    {
      id: 'sonia_singh',
      name: 'Sonia Singh',
      designation: 'Mother',
      corruptionStage: 1,
      submissionLevel: 0,
      loyalty: 95,
      lustMeter: 88, // High general lust, sexually frustrated
      specialSkills: ['Financial Management', 'Household Authority', 'Maternal Instincts'],
      status: 'Asset',
      profileImage: '/images/sonia.jpg'
    },
    {
      id: 'natasha_singh',
      name: 'Natasha Singh',
      designation: 'Sister (Elder)',
      corruptionStage: 1,
      submissionLevel: 0,
      loyalty: 85,
      lustMeter: 45,
      specialSkills: ['HR Networks', 'Social Connections', 'Professional Ambition'],
      status: 'Pawn',
      profileImage: '/images/natasha.jpg'
    },
    {
      id: 'tanya_singh',
      name: 'Tanya Singh',
      designation: 'Sister (Younger)',
      corruptionStage: 1,
      submissionLevel: 0,
      loyalty: 80,
      lustMeter: 35,
      specialSkills: ['Tech Savvy', 'Social Media', 'Youthful Energy'],
      status: 'Pawn',
      profileImage: '/images/tanya.jpg'
    }
  ],

  // Inventory
  inventory: [
    {
      id: 'sleeping_pills',
      name: 'Sleeping Pills (Mild)',
      type: 'Consumable',
      description: 'Mild sedative pills for inducing deep sleep',
      quantity: 1
    }
  ],
});

const createInitialQuests = (): Quest[] => [
  {
    id: 'queen_conquest_2',
    title: 'The Queen\'s Conquest (Stage 2)',
    type: 'Dark',
    target: 'Sonia Singh',
    objective: 'The seed of corruption has been planted. Now, it must be watered with a physical transgression. Administer a second dose of [Sleeping Pills]. While she sleeps, violate her physically.',
    status: 'Active',
    requirements: {
      item: 'Sleeping Pills (Mild)'
    },
    rewards: {
      systemPoints: 1000,
      deviantXP: 1000,
      newSkill: 'Dominator\'s Touch',
      haremUpdate: {
        targetId: 'sonia_singh',
        submissionChange: 25,
        corruptionStageUp: true
      }
    }
  },
  {
    id: 'serpent_nest',
    title: 'The Serpent\'s Nest',
    type: 'Dark',
    target: 'Natasha Singh',
    objective: 'The home is the first domain a King must conquer. Assert your hidden dominance. Install a micro-spy camera in Natasha Singh\'s private bedroom.',
    status: 'Active',
    requirements: {},
    rewards: {
      systemPoints: 50,
      deviantXP: 100,
      newSkill: 'Surveillance Mastery'
    }
  },
  {
    id: 'watering_hole',
    title: 'The Watering Hole',
    type: 'Dark',
    target: 'Family',
    objective: 'The most private moments reveal the truest selves. Gain access to the sanctum sanctorum of your female family members. Install a waterproof nano-camera in the shared family washroom.',
    status: 'Active',
    requirements: {},
    rewards: {
      systemPoints: 100,
      deviantXP: 200,
      newSkill: 'Intimacy Tracker'
    }
  }
];

const createInitialEventLog = (): LogEntry[] => [
  {
    id: 'log_1',
    timestamp: new Date('2012-08-28T22:15:00Z'),
    content: 'Quest Accepted: The Queen\'s Conquest (Stage 1)',
    type: 'Quest'
  },
  {
    id: 'log_2',
    timestamp: new Date('2012-08-28T23:30:00Z'),
    content: 'Bonus Objective Complete: Psychoactive Catalyst. Trait Unlocked: [Incestuous Aura]',
    type: 'Reward'
  },
  {
    id: 'log_3',
    timestamp: new Date('2012-08-28T23:31:00Z'),
    content: 'New Quest Available: The Queen\'s Conquest (Stage 2)',
    type: 'Quest'
  },
  {
    id: 'system_boot',
    timestamp: new Date(),
    content: '[SYSTEM BOOT SEQUENCE COMPLETE] [CHIMERA OS v1.0 ONLINE] [BIOMETRIC LINK ESTABLISHED... WELCOME, VIKRAM SINGH]',
    type: 'System'
  }
];

export function useSystemState() {
  const [systemState, setSystemState] = useState<SystemState>({
    profile: createInitialProfile(),
    activeQuests: createInitialQuests(),
    eventLog: createInitialEventLog(),
    gameSettings: initialSettings,
    isLoading: false,
  });

  // Load saved state on mount
  useEffect(() => {
    const savedState = localStorage.getItem('vikram_system_state');
    if (savedState) {
      try {
        const parsed = JSON.parse(savedState);
        // Convert timestamp strings back to Date objects
        if (parsed.eventLog) {
          parsed.eventLog = parsed.eventLog.map((entry: any) => ({
            ...entry,
            timestamp: new Date(entry.timestamp)
          }));
        }
        setSystemState(prevState => ({
          ...prevState,
          ...parsed,
          isLoading: false,
        }));
      } catch (error) {
        console.error('Failed to load saved state:', error);
      }
    }
  }, []);

  // Auto-save when state changes
  useEffect(() => {
    if (systemState.gameSettings.autoSave) {
      localStorage.setItem('vikram_system_state', JSON.stringify(systemState));
    }
  }, [systemState]);

  const updateProfile = useCallback((updates: Partial<VikramProfile>) => {
    setSystemState(prevState => ({
      ...prevState,
      profile: { ...prevState.profile, ...updates },
    }));
  }, []);

  const addLogEntry = useCallback((entry: Omit<LogEntry, 'id' | 'timestamp'>) => {
    const newEntry: LogEntry = {
      id: `log_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      ...entry
    };
    
    setSystemState(prevState => ({
      ...prevState,
      eventLog: [...prevState.eventLog, newEntry],
    }));
  }, []);

  const updateQuest = useCallback((questId: string, updates: Partial<Quest>) => {
    setSystemState(prevState => ({
      ...prevState,
      activeQuests: prevState.activeQuests.map(quest =>
        quest.id === questId ? { ...quest, ...updates } : quest
      ),
    }));
  }, []);

  const completeQuest = useCallback((questId: string) => {
    const quest = systemState.activeQuests.find(q => q.id === questId);
    if (!quest) return;

    // Apply rewards
    const rewards = quest.rewards;
    const profileUpdates: Partial<VikramProfile> = {};

    if (rewards.systemPoints) {
      profileUpdates.systemPoints = systemState.profile.systemPoints + rewards.systemPoints;
    }
    if (rewards.deviantXP) {
      profileUpdates.deviantXP = systemState.profile.deviantXP + rewards.deviantXP;
    }
    if (rewards.alphaPoints) {
      profileUpdates.alphaPoints = systemState.profile.alphaPoints + rewards.alphaPoints;
    }
    if (rewards.newSkill) {
      profileUpdates.skills = [...systemState.profile.skills, rewards.newSkill];
    }
    if (rewards.newTitle) {
      profileUpdates.title = rewards.newTitle;
    }

    // Update harem member if specified
    if (rewards.haremUpdate) {
      const haremUpdate = rewards.haremUpdate;
      profileUpdates.harem = systemState.profile.harem.map(member => {
        if (member.id === haremUpdate.targetId) {
          const updatedMember = { ...member };
          if (haremUpdate.submissionChange) {
            updatedMember.submissionLevel = Math.min(100, Math.max(0, 
              member.submissionLevel + haremUpdate.submissionChange
            ));
          }
          if (haremUpdate.loyaltyChange) {
            updatedMember.loyalty = Math.min(100, Math.max(0, 
              member.loyalty + haremUpdate.loyaltyChange
            ));
          }
          if (haremUpdate.corruptionStageUp && member.corruptionStage < 5) {
            updatedMember.corruptionStage = (member.corruptionStage + 1) as 1 | 2 | 3 | 4 | 5;
          }
          return updatedMember;
        }
        return member;
      });
    }

    updateProfile(profileUpdates);
    updateQuest(questId, { status: 'Completed' });
    addLogEntry({
      content: `Quest completed: ${quest.title}`,
      type: 'Quest'
    });

    // Add reward log entries
    if (rewards.systemPoints) {
      addLogEntry({
        content: `Earned ${rewards.systemPoints} System Points`,
        type: 'Reward'
      });
    }
  }, [systemState, updateProfile, updateQuest, addLogEntry]);

  const convertWealth = useCallback((inrAmount: number) => {
    if (inrAmount > systemState.profile.wealth) return;

    const spGained = Math.floor(inrAmount / systemState.gameSettings.wealthConversionRate);
    
    updateProfile({
      wealth: systemState.profile.wealth - inrAmount,
      systemPoints: systemState.profile.systemPoints + spGained
    });

    addLogEntry({
      content: `Converted ₹${inrAmount.toLocaleString()} to ${spGained} SP`,
      type: 'System'
    });
  }, [systemState, updateProfile, addLogEntry]);

  const purchaseItem = useCallback((item: SystemItem, price: number) => {
    if (price > systemState.profile.systemPoints) return;

    const existingItem = systemState.profile.inventory.find(i => i.id === item.id);
    
    if (existingItem) {
      updateProfile({
        systemPoints: systemState.profile.systemPoints - price,
        inventory: systemState.profile.inventory.map(i =>
          i.id === item.id ? { ...i, quantity: i.quantity + 1 } : i
        )
      });
    } else {
      updateProfile({
        systemPoints: systemState.profile.systemPoints - price,
        inventory: [...systemState.profile.inventory, { ...item, quantity: 1 }]
      });
    }

    addLogEntry({
      content: `Purchased ${item.name} for ${price} SP`,
      type: 'System'
    });
  }, [systemState, updateProfile, addLogEntry]);

  const updateSettings = useCallback((updates: Partial<GameSettings>) => {
    setSystemState(prevState => ({
      ...prevState,
      gameSettings: { ...prevState.gameSettings, ...updates },
    }));
  }, []);

  const setLoading = useCallback((isLoading: boolean) => {
    setSystemState(prevState => ({
      ...prevState,
      isLoading,
    }));
  }, []);

  const resetSystem = useCallback(() => {
    setSystemState({
      profile: createInitialProfile(),
      activeQuests: createInitialQuests(),
      eventLog: createInitialEventLog(),
      gameSettings: systemState.gameSettings, // Keep settings
      isLoading: false,
    });
  }, [systemState.gameSettings]);

  const saveSystem = useCallback(() => {
    try {
      const saveData = {
        profile: systemState.profile,
        activeQuests: systemState.activeQuests,
        eventLog: systemState.eventLog,
        gameSettings: systemState.gameSettings,
        timestamp: new Date().toISOString(),
        version: '1.0'
      };

      localStorage.setItem('vikram-system-save', JSON.stringify(saveData));

      addLogEntry({
        content: 'System state saved successfully',
        type: 'System'
      });

      return true;
    } catch (error) {
      console.error('Failed to save system state:', error);
      addLogEntry({
        content: 'Failed to save system state',
        type: 'System'
      });
      return false;
    }
  }, [systemState, addLogEntry]);

  const loadSystem = useCallback(() => {
    try {
      const savedData = localStorage.getItem('vikram-system-save');
      if (!savedData) {
        addLogEntry({
          content: 'No saved game found',
          type: 'System'
        });
        return false;
      }

      const saveData = JSON.parse(savedData);

      // Validate save data structure
      if (!saveData.profile || !saveData.activeQuests || !saveData.eventLog) {
        throw new Error('Invalid save data structure');
      }

      setSystemState(prevState => ({
        ...prevState,
        profile: saveData.profile,
        activeQuests: saveData.activeQuests,
        eventLog: saveData.eventLog,
        gameSettings: { ...prevState.gameSettings, ...saveData.gameSettings },
        isLoading: false
      }));

      addLogEntry({
        content: `Game loaded from ${new Date(saveData.timestamp).toLocaleString()}`,
        type: 'System'
      });

      return true;
    } catch (error) {
      console.error('Failed to load system state:', error);
      addLogEntry({
        content: 'Failed to load saved game',
        type: 'System'
      });
      return false;
    }
  }, [addLogEntry]);

  // Auto-save functionality
  useEffect(() => {
    if (systemState.gameSettings.autoSave && !systemState.isLoading) {
      const autoSaveInterval = setInterval(() => {
        saveSystem();
      }, 300000); // Auto-save every 5 minutes

      return () => clearInterval(autoSaveInterval);
    }
  }, [systemState.gameSettings.autoSave, systemState.isLoading, saveSystem]);

  return {
    systemState,
    updateProfile,
    addLogEntry,
    updateQuest,
    completeQuest,
    convertWealth,
    purchaseItem,
    updateSettings,
    setLoading,
    resetSystem,
    saveSystem,
    loadSystem,
  };
}
