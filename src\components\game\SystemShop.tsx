'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@radix-ui/react-tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { SystemItem, VikramProfile } from '@/types/game';
import { 
  ShoppingCart, 
  Star, 
  Pill, 
  Eye, 
  Zap, 
  Gift,
  Camera,
  Headphones,
  Smartphone,
  Watch,
  Briefcase,
  Heart,
  Brain,
  Shield,
  TrendingUp,
  Coins
} from 'lucide-react';

interface SystemShopProps {
  profile: VikramProfile;
  onPurchase?: (item: SystemItem, price: number) => void;
  onPreview?: (item: SystemItem) => void;
}

// Shop inventory data
const SHOP_ITEMS: SystemItem[] = [
  // Consumables
  {
    id: 'stamina_boost',
    name: '<PERSON>amina Booster',
    type: 'Consumable',
    description: 'Instantly restores 50 stamina points. Essential for extended activities.',
    quantity: 0
  },
  {
    id: 'lust_enhancer',
    name: 'Lust Enhancer',
    type: 'Consumable',
    description: 'Increases target\'s lust meter by 25 points. Temporary but effective.',
    quantity: 0
  },
  {
    id: 'confidence_pill',
    name: 'Confidence Pill',
    type: 'Consumable',
    description: 'Temporarily boosts dominance by 10 points for 24 hours.',
    quantity: 0
  },
  {
    id: 'memory_enhancer',
    name: 'Memory Enhancer',
    type: 'Consumable',
    description: 'Improves manipulation effectiveness by remembering target preferences.',
    quantity: 0
  },
  
  // Espionage
  {
    id: 'surveillance_kit',
    name: 'Surveillance Kit',
    type: 'Espionage',
    description: 'Advanced monitoring equipment for gathering intelligence on targets.',
    quantity: 0
  },
  {
    id: 'voice_recorder',
    name: 'Voice Recorder',
    type: 'Espionage',
    description: 'High-quality recording device for capturing conversations.',
    quantity: 0
  },
  {
    id: 'phone_tracker',
    name: 'Phone Tracker',
    type: 'Espionage',
    description: 'Software to monitor target\'s phone activity and location.',
    quantity: 0
  },
  {
    id: 'social_analyzer',
    name: 'Social Media Analyzer',
    type: 'Espionage',
    description: 'AI tool to analyze target\'s social media patterns and vulnerabilities.',
    quantity: 0
  },
  
  // Enhancements
  {
    id: 'vocal_resonance',
    name: 'Vocal Resonance Enhancement',
    type: 'Enhancement',
    description: 'Permanently improves voice persuasion abilities. Increases manipulation by 5.',
    quantity: 0
  },
  {
    id: 'predator_stealth',
    name: 'Predator\'s Stealth',
    type: 'Enhancement',
    description: 'Enhanced ability to move undetected. Increases deception by 5.',
    quantity: 0
  },
  {
    id: 'alpha_presence',
    name: 'Alpha Presence',
    type: 'Enhancement',
    description: 'Commanding aura that demands respect. Increases dominance by 5.',
    quantity: 0
  },
  {
    id: 'system_integration',
    name: 'System Integration Upgrade',
    type: 'Enhancement',
    description: 'Deeper connection with the System. Unlocks advanced features.',
    quantity: 0
  },
  
  // Gifts
  {
    id: 'luxury_jewelry',
    name: 'Luxury Jewelry',
    type: 'Gift',
    description: 'Expensive jewelry to increase target loyalty and submission.',
    quantity: 0
  },
  {
    id: 'designer_clothes',
    name: 'Designer Clothes',
    type: 'Gift',
    description: 'High-end fashion items that make targets feel special.',
    quantity: 0
  },
  {
    id: 'tech_gadget',
    name: 'Latest Tech Gadget',
    type: 'Gift',
    description: 'Cutting-edge technology that impresses and creates dependency.',
    quantity: 0
  },
  {
    id: 'experience_package',
    name: 'Exclusive Experience',
    type: 'Gift',
    description: 'VIP access to exclusive events and experiences.',
    quantity: 0
  }
];

const ITEM_PRICES: Record<string, number> = {
  // Consumables (50-200 SP)
  'stamina_boost': 75,
  'lust_enhancer': 150,
  'confidence_pill': 100,
  'memory_enhancer': 125,
  
  // Espionage (200-500 SP)
  'surveillance_kit': 400,
  'voice_recorder': 200,
  'phone_tracker': 350,
  'social_analyzer': 500,
  
  // Enhancements (500-1500 SP)
  'vocal_resonance': 800,
  'predator_stealth': 750,
  'alpha_presence': 900,
  'system_integration': 1500,
  
  // Gifts (100-400 SP)
  'luxury_jewelry': 300,
  'designer_clothes': 250,
  'tech_gadget': 400,
  'experience_package': 350
};

export function SystemShop({ profile, onPurchase, onPreview }: SystemShopProps) {
  const [selectedCategory, setSelectedCategory] = useState<SystemItem['type']>('Consumable');

  const getItemIcon = (item: SystemItem) => {
    switch (item.id) {
      case 'stamina_boost': return Heart;
      case 'lust_enhancer': return TrendingUp;
      case 'confidence_pill': return Shield;
      case 'memory_enhancer': return Brain;
      case 'surveillance_kit': return Camera;
      case 'voice_recorder': return Headphones;
      case 'phone_tracker': return Smartphone;
      case 'social_analyzer': return Eye;
      case 'vocal_resonance': return Zap;
      case 'predator_stealth': return Eye;
      case 'alpha_presence': return Shield;
      case 'system_integration': return Star;
      case 'luxury_jewelry': return Star;
      case 'designer_clothes': return Gift;
      case 'tech_gadget': return Smartphone;
      case 'experience_package': return Briefcase;
      default: return ShoppingCart;
    }
  };

  const getItemsByCategory = (category: SystemItem['type']) => {
    return SHOP_ITEMS.filter(item => item.type === category);
  };

  const canAfford = (itemId: string) => {
    const price = ITEM_PRICES[itemId] || 0;
    return profile.systemPoints >= price;
  };

  const handlePurchase = (item: SystemItem) => {
    const price = ITEM_PRICES[item.id] || 0;
    if (canAfford(item.id)) {
      onPurchase?.(item, price);
    }
  };

  const ItemCard = ({ item }: { item: SystemItem }) => {
    const Icon = getItemIcon(item);
    const price = ITEM_PRICES[item.id] || 0;
    const affordable = canAfford(item.id);
    
    return (
      <Card className="transition-all duration-200 hover:shadow-lg hover:shadow-primary/20">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <div className="w-12 h-12 rounded-lg bg-primary/10 flex items-center justify-center flex-shrink-0">
              <Icon className="w-6 h-6 text-primary" />
            </div>
            
            <div className="flex-1 min-w-0">
              <h3 className="font-semibold text-sm mb-1">{item.name}</h3>
              <p className="text-xs text-muted-foreground mb-3 line-clamp-2">
                {item.description}
              </p>
              
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  <Star className="w-4 h-4 text-primary" />
                  <span className={`text-sm font-bold ${affordable ? 'text-primary' : 'text-red-400'}`}>
                    {price} SP
                  </span>
                </div>
                
                <div className="flex gap-2">
                  {onPreview && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onPreview(item)}
                    >
                      <Eye className="w-3 h-3" />
                    </Button>
                  )}
                  <Button
                    variant={affordable ? "system" : "outline"}
                    size="sm"
                    disabled={!affordable}
                    onClick={() => handlePurchase(item)}
                  >
                    {affordable ? 'Buy' : 'Insufficient SP'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <ShoppingCart className="w-5 h-5 text-primary" />
          <h2 className="text-lg font-semibold text-foreground">System Shop</h2>
        </div>
        
        <div className="flex items-center gap-2 px-3 py-2 bg-primary/10 rounded-lg border border-primary/20">
          <Coins className="w-4 h-4 text-primary" />
          <span className="text-sm font-bold text-primary">{profile.systemPoints} SP</span>
        </div>
      </div>
      
      {/* Shop Categories */}
      <Tabs value={selectedCategory} onValueChange={(value) => setSelectedCategory(value as SystemItem['type'])}>
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="Consumable" className="flex items-center gap-2">
            <Pill className="w-4 h-4" />
            <span>Consumables</span>
          </TabsTrigger>
          <TabsTrigger value="Espionage" className="flex items-center gap-2">
            <Eye className="w-4 h-4" />
            <span>Espionage</span>
          </TabsTrigger>
          <TabsTrigger value="Enhancement" className="flex items-center gap-2">
            <Zap className="w-4 h-4" />
            <span>Enhancements</span>
          </TabsTrigger>
          <TabsTrigger value="Gift" className="flex items-center gap-2">
            <Gift className="w-4 h-4" />
            <span>Gifts</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="Consumable" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {getItemsByCategory('Consumable').map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="Espionage" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {getItemsByCategory('Espionage').map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="Enhancement" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {getItemsByCategory('Enhancement').map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
          </div>
        </TabsContent>

        <TabsContent value="Gift" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {getItemsByCategory('Gift').map((item) => (
              <ItemCard key={item.id} item={item} />
            ))}
          </div>
        </TabsContent>
      </Tabs>
      
      {/* Shop Info */}
      <Card variant="system" className="p-4">
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <Star className="w-4 h-4 text-primary" />
          <span>System Points (SP) are earned through completing quests and achieving milestones</span>
        </div>
      </Card>
    </div>
  );
}
