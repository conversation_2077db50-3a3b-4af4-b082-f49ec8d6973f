'use client';

import React, { useState } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { HaremMember } from '@/types/game';
import { 
  Users, 
  Heart, 
  Shield, 
  Star, 
  Eye, 
  Crown,
  Target,
  Gift,
  MessageCircle,
  TrendingUp,
  Award,
  X
} from 'lucide-react';

interface HaremManagementProps {
  harem: HaremMember[];
  onUpdateMember: (memberId: string, updates: Partial<HaremMember>) => void;
  onMemberInteraction?: (member: HaremMember, interactionType: string) => void;
}

export function HaremManagement({ harem, onUpdateMember, onMemberInteraction }: HaremManagementProps) {
  const [selectedMember, setSelectedMember] = useState<HaremMember | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const getCorruptionStageColor = (stage: number) => {
    switch (stage) {
      case 1: return 'text-green-400 border-green-400';
      case 2: return 'text-yellow-400 border-yellow-400';
      case 3: return 'text-orange-400 border-orange-400';
      case 4: return 'text-red-400 border-red-400';
      case 5: return 'text-purple-400 border-purple-400';
      default: return 'text-muted-foreground border-muted';
    }
  };

  const getStatusColor = (status: HaremMember['status']) => {
    switch (status) {
      case 'Asset': return 'text-green-400 bg-green-400/10';
      case 'Pawn': return 'text-yellow-400 bg-yellow-400/10';
      case 'Queen': return 'text-purple-400 bg-purple-400/10';
      case 'Broken': return 'text-red-400 bg-red-400/10';
      default: return 'text-muted-foreground bg-muted/10';
    }
  };

  const getCorruptionStageDescription = (stage: number) => {
    switch (stage) {
      case 1: return 'Initial Contact';
      case 2: return 'Growing Interest';
      case 3: return 'Emotional Dependency';
      case 4: return 'Physical Submission';
      case 5: return 'Complete Devotion';
      default: return 'Unknown';
    }
  };

  const handleMemberClick = (member: HaremMember) => {
    setSelectedMember(member);
  };

  const handleAction = (member: HaremMember, action: string) => {
    onMemberInteraction?.(member, action);
  };

  const MemberCard = ({ member }: { member: HaremMember }) => (
    <Card 
      variant="harem" 
      className="cursor-pointer transition-all duration-200"
      onClick={() => handleMemberClick(member)}
    >
      <CardContent className="p-4">
        <div className="flex items-start gap-3">
          {/* Profile Image Placeholder */}
          <div className="w-12 h-12 rounded-full bg-gradient-to-br from-primary/20 to-purple-600/20 flex items-center justify-center flex-shrink-0">
            <Users className="w-6 h-6 text-primary" />
          </div>
          
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <h3 className="font-semibold text-sm truncate">{member.name}</h3>
              <span className={`text-xs px-2 py-1 rounded-full border ${getCorruptionStageColor(member.corruptionStage)}`}>
                S{member.corruptionStage}
              </span>
            </div>
            
            <p className="text-xs text-muted-foreground mb-2">{member.designation}</p>
            
            <div className="space-y-2">
              {/* Submission Level */}
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">Submission</span>
                  <span className="text-xs text-blue-400">{member.submissionLevel}%</span>
                </div>
                <Progress 
                  value={member.submissionLevel} 
                  variant="submission"
                  className="h-1"
                />
              </div>
              
              {/* Loyalty */}
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">Loyalty</span>
                  <span className="text-xs text-green-400">{member.loyalty}%</span>
                </div>
                <Progress 
                  value={member.loyalty} 
                  variant="loyalty"
                  className="h-1"
                />
              </div>
              
              {/* Lust Meter */}
              <div className="space-y-1">
                <div className="flex items-center justify-between">
                  <span className="text-xs text-muted-foreground">Lust</span>
                  <span className="text-xs text-pink-400">{member.lustMeter}%</span>
                </div>
                <Progress 
                  value={member.lustMeter} 
                  variant="lust"
                  className="h-1"
                />
              </div>
            </div>
            
            {/* Status */}
            <div className="mt-2">
              <span className={`text-xs px-2 py-1 rounded-full ${getStatusColor(member.status)}`}>
                {member.status}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const MemberDetailModal = ({ member }: { member: HaremMember }) => (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card variant="system" className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader className="pb-4">
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2 text-primary">
              <Users className="w-5 h-5" />
              {member.name}
            </CardTitle>
            <Button 
              variant="ghost" 
              size="sm"
              onClick={() => setSelectedMember(null)}
            >
              <X className="w-4 h-4" />
            </Button>
          </div>
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">{member.designation}</span>
            <span className={`text-xs px-2 py-1 rounded-full border ${getCorruptionStageColor(member.corruptionStage)}`}>
              Stage {member.corruptionStage}: {getCorruptionStageDescription(member.corruptionStage)}
            </span>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-6">
          {/* Profile Image */}
          <div className="text-center">
            <div className="w-24 h-24 mx-auto rounded-full bg-gradient-to-br from-primary/20 to-purple-600/20 flex items-center justify-center">
              <Users className="w-12 h-12 text-primary" />
            </div>
          </div>
          
          {/* Stats */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="space-y-1">
                <div className="flex items-center justify-center gap-1">
                  <Shield className="w-4 h-4 text-blue-400" />
                  <span className="text-sm">Submission</span>
                </div>
                <Progress 
                  value={member.submissionLevel} 
                  variant="submission"
                  showGlow={true}
                />
                <span className="text-lg font-bold text-blue-400">{member.submissionLevel}%</span>
              </div>
            </div>
            
            <div className="text-center">
              <div className="space-y-1">
                <div className="flex items-center justify-center gap-1">
                  <Heart className="w-4 h-4 text-green-400" />
                  <span className="text-sm">Loyalty</span>
                </div>
                <Progress 
                  value={member.loyalty} 
                  variant="loyalty"
                  showGlow={true}
                />
                <span className="text-lg font-bold text-green-400">{member.loyalty}%</span>
              </div>
            </div>
            
            <div className="text-center">
              <div className="space-y-1">
                <div className="flex items-center justify-center gap-1">
                  <Star className="w-4 h-4 text-pink-400" />
                  <span className="text-sm">Lust</span>
                </div>
                <Progress 
                  value={member.lustMeter} 
                  variant="lust"
                  showGlow={true}
                />
                <span className="text-lg font-bold text-pink-400">{member.lustMeter}%</span>
              </div>
            </div>
          </div>
          
          {/* Status */}
          <div className="text-center">
            <span className={`text-sm px-3 py-2 rounded-full ${getStatusColor(member.status)}`}>
              Current Status: {member.status}
            </span>
          </div>
          
          {/* Special Skills */}
          {member.specialSkills.length > 0 && (
            <div className="space-y-2">
              <h3 className="text-sm font-semibold text-foreground">Special Skills</h3>
              <div className="flex flex-wrap gap-2">
                {member.specialSkills.map((skill, index) => (
                  <span 
                    key={index}
                    className="px-2 py-1 text-xs bg-secondary/20 border border-secondary/30 rounded-full text-secondary"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          {/* Action Buttons */}
          <div className="grid grid-cols-2 gap-3">
            <Button 
              variant="system" 
              onClick={() => handleAction(member, 'interact')}
            >
              <MessageCircle className="w-4 h-4 mr-2" />
              Interact
            </Button>
            <Button 
              variant="corruption" 
              onClick={() => handleAction(member, 'corrupt')}
            >
              <TrendingUp className="w-4 h-4 mr-2" />
              Corrupt
            </Button>
            <Button 
              variant="cyber" 
              onClick={() => handleAction(member, 'gift')}
            >
              <Gift className="w-4 h-4 mr-2" />
              Give Gift
            </Button>
            <Button 
              variant="dominance" 
              onClick={() => handleAction(member, 'dominate')}
            >
              <Crown className="w-4 h-4 mr-2" />
              Dominate
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Users className="w-5 h-5 text-primary" />
          <h2 className="text-lg font-semibold text-foreground">Harem Management</h2>
          <span className="text-sm text-muted-foreground">({harem.length} members)</span>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'grid' ? 'system' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            Grid
          </Button>
          <Button
            variant={viewMode === 'list' ? 'system' : 'outline'}
            size="sm"
            onClick={() => setViewMode('list')}
          >
            List
          </Button>
        </div>
      </div>
      
      {/* Members Grid/List */}
      {harem.length === 0 ? (
        <Card className="p-8 text-center">
          <Users className="w-12 h-12 mx-auto mb-4 text-muted-foreground opacity-50" />
          <h3 className="text-lg font-semibold text-foreground mb-2">No Harem Members</h3>
          <p className="text-muted-foreground">Complete quests to add members to your harem</p>
        </Card>
      ) : (
        <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4' : 'space-y-3'}>
          {harem.map((member) => (
            <MemberCard key={member.id} member={member} />
          ))}
        </div>
      )}
      
      {/* Detail Modal */}
      {selectedMember && (
        <MemberDetailModal member={selectedMember} />
      )}
    </div>
  );
}
